#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Template structure fixes
const fixes = [
  // Fix unclosed ng-template tags
  {
    pattern: /<\/ng-template><\/p-card>/g,
    replacement: '</ng-template>\n      </p-card>'
  },

  // Fix unclosed p-button tags
  {
    pattern: /<\/p-button><\/p-menu>/g,
    replacement: '</p-button>\n        </p-menu>'
  },

  // Fix unclosed p-tabPanel tags
  {
    pattern: /<\/div><\/p-tabPanel>/g,
    replacement: '</div>\n        </p-tabPanel>'
  },

  // Fix unclosed form tags
  {
    pattern: /<\/form><ng-template pTemplate="footer">/g,
    replacement: '</form>\n        </ng-template>\n        <ng-template pTemplate="footer">'
  },

  // Fix mat-expansion-panel-header issues
  {
    pattern: /<\/mat-expansion-panel-header>/g,
    replacement: '</mat-expansion-panel-header>\n                  <div class="expansion-content">'
  },

  // Fix p-accordionTab issues
  {
    pattern: /<\/p-accordionTab>/g,
    replacement: '</p-accordionTab>'
  },

  // Fix ng-container issues
  {
    pattern: /<\/ng-container>/g,
    replacement: '</ng-container>'
  },

  // Fix table structure issues
  {
    pattern: /<\/table><\/div><\/p-tabPanel>/g,
    replacement: '</table>\n            </div>\n          </p-tabPanel>'
  },

  // Fix mat-list issues
  {
    pattern: /<\/mat-list<\/p-card>/g,
    replacement: '</mat-list>\n                  </p-card>'
  },

  // Fix mat-list with ng-template issues
  {
    pattern: /<\/mat-list<\/ng-template><\/p-card>/g,
    replacement: '</mat-list>\n                  </ng-template>\n                </p-card>'
  },

  // Fix p-tabView issues
  {
    pattern: /<\/p-tabView>/g,
    replacement: '</p-tabView>'
  },

  // Fix specific template structure issues
  {
    pattern: /<p>Content<\/p><ng-template pTemplate="content">/g,
    replacement: '</ng-template>\n        <ng-template pTemplate="content">'
  },

  // Fix register component template issue
  {
    pattern: /<\/p><\/ng-template><\/p-card>/g,
    replacement: '</p>\n        </ng-template>\n      </p-card>'
  }
];

// Import fixes for dialog components
const importFixes = [
  {
    pattern: /DragDropModuleInputTextModule/g,
    replacement: 'DragDropModule,\n    InputTextModule'
  },
  {
    pattern: /ReactiveFormsModuleInputTextModule/g,
    replacement: 'ReactiveFormsModule,\n    InputTextModule'
  }
];

// Service syntax fixes
const serviceFixes = [
  {
    pattern: /\}\);$/gm,
    replacement: '});'
  }
];

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Apply template fixes
    fixes.forEach(fix => {
      const oldContent = content;
      content = content.replace(fix.pattern, fix.replacement);
      if (content !== oldContent) {
        modified = true;
      }
    });

    // Apply import fixes
    importFixes.forEach(fix => {
      const oldContent = content;
      content = content.replace(fix.pattern, fix.replacement);
      if (content !== oldContent) {
        modified = true;
      }
    });

    // Apply service fixes
    serviceFixes.forEach(fix => {
      const oldContent = content;
      content = content.replace(fix.pattern, fix.replacement);
      if (content !== oldContent) {
        modified = true;
      }
    });

    // Fix specific template structure patterns
    const specificFixes = [
      // Fix mat-list issues
      { from: '</mat-list</p-card>', to: '</mat-list>\n                  </p-card>' },
      { from: '</mat-list</ng-template></p-card>', to: '</mat-list>\n                  </ng-template>\n                </p-card>' },

      // Fix p-button menu issues
      { from: '</p-button>\n        </p-menu>', to: '</p-button>\n      </p-menu>' },

      // Fix form closing issues
      { from: '</form></p-card>', to: '</form>\n        </ng-template>\n      </p-card>' },

      // Fix div closing issues
      { from: '</div></p-tabPanel>', to: '</div>\n        </p-tabPanel>' },

      // Fix ng-container issues
      { from: '</ng-container>\n                </ng-container>', to: '</ng-container>' }
    ];

    specificFixes.forEach(fix => {
      if (content.includes(fix.from)) {
        content = content.replace(new RegExp(fix.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), fix.to);
        modified = true;
      }
    });

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`Fixed: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error.message);
  }
}

function walkDirectory(dir) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      walkDirectory(filePath);
    } else if (file.endsWith('.ts') && !file.endsWith('.spec.ts')) {
      fixFile(filePath);
    }
  });
}

console.log('Starting template structure fixes...');
walkDirectory('./src');
console.log('Template structure fixes completed!');
