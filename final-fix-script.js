#!/usr/bin/env node

/**
 * Final Fix Script - Resolves all remaining build errors
 * Fixes MessageService syntax, template structures, imports, and dialog configs
 */

const fs = require('fs');
const path = require('path');

class FinalFixScript {
  constructor() {
    this.srcPath = './src/app';
    this.fixes = {
      syntaxErrors: 0,
      templateErrors: 0,
      importErrors: 0,
      dialogErrors: 0
    };
  }

  // Fix MessageService syntax errors (missing semicolons)
  fixMessageServiceSyntax(content) {
    let fixed = content;
    let changes = 0;

    // Fix specific syntax errors with extra parentheses and missing semicolons
    const syntaxFixes = [
      // Fix: })); -> });
      {
        from: /this\.messageService\.add\(\{[^}]*\}\)\);/g,
        to: (match) => match.replace(/\)\);/, ');')
      },
      // Fix broken calls with template literals
      {
        from: /this\.messageService\.add\(\{\s*severity:\s*'info',\s*summary:\s*([^}]+)\s*\}\)\);/g,
        to: 'this.messageService.add({ severity: \'info\', summary: $1 });'
      },
      // Fix calls with string concatenation
      {
        from: /this\.messageService\.add\(\{\s*severity:\s*'info',\s*summary:\s*'([^']*)'?\s*\+\s*([^}]+)\s*\}\)\);/g,
        to: 'this.messageService.add({ severity: \'info\', summary: \'$1\' + $2 });'
      },
      // Fix calls with error.message
      {
        from: /this\.messageService\.add\(\{\s*severity:\s*'info',\s*summary:\s*([^}]*error\.message[^}]*)\s*\}\)\);/g,
        to: 'this.messageService.add({ severity: \'info\', summary: $1 });'
      }
    ];

    syntaxFixes.forEach(fix => {
      if (typeof fix.to === 'function') {
        fixed = fixed.replace(fix.from, fix.to);
      } else {
        fixed = fixed.replace(fix.from, fix.to);
      }
      changes++;
    });

    // Fix any remaining double parentheses issues
    fixed = fixed.replace(/\}\)\);/g, '});');

    this.fixes.syntaxErrors += changes;
    return fixed;
  }

  // Fix template structure errors
  fixTemplateStructures(content) {
    let fixed = content;
    let changes = 0;

    // Fix broken template structures
    const templateFixes = [
      // Fix extra closing p-card tags
      {
        from: /<\/ng-template>\s*<\/p-card>\s*<\/p-card>/g,
        to: '</ng-template></p-card>'
      },
      // Fix broken ng-template + p-card combinations
      {
        from: /<\/div>\s*<\/ng-template>\s*<\/p-card>/g,
        to: '</div></ng-template></p-card>'
      },
      // Fix broken p-button structures
      {
        from: /<\/p-button>\s*<\/p-button>/g,
        to: '</p-button>'
      },
      // Fix broken p-tabPanel structures
      {
        from: /<\/div>\s*<\/p-tabPanel>\s*<\/p-tabPanel>/g,
        to: '</div></p-tabPanel>'
      },
      {
        from: /<\/p-tabPanel>\s*<\/p-tabPanel>/g,
        to: '</p-tabPanel>'
      },
      // Fix broken ng-container structures
      {
        from: /<\/ng-container>\s*<\/ng-container>/g,
        to: '</ng-container>'
      },
      // Fix broken table structures
      {
        from: /<\/table>\s*<\/table>/g,
        to: '</table>'
      },
      // Fix broken div structures
      {
        from: /<\/div>\s*<\/div>\s*<\/div>/g,
        to: '</div></div>'
      },
      {
        from: /<\/div>\s*<\/p-card>/g,
        to: '</div></ng-template></p-card>'
      },
      // Fix broken p-toolbar structures
      {
        from: /<\/p-toolbar>\s*<\/p-toolbar>/g,
        to: '</p-toolbar>'
      },
      // Fix broken p-menu structures
      {
        from: /<\/p-menu>\s*<\/p-menu>/g,
        to: '</p-menu>'
      },
      // Fix broken form structures
      {
        from: /<\/form>\s*<\/form>/g,
        to: '</form>'
      },
      // Fix broken ng-template structures
      {
        from: /<\/ng-template>\s*<\/ng-template>/g,
        to: '</ng-template>'
      },
      // Fix specific broken structures from build errors
      {
        from: /<\/div>\s*<\/ng-template>\s*<\/p-card>/g,
        to: '</div></ng-template></p-card>'
      },
      {
        from: /<\/p>\s*<\/p-card>/g,
        to: '</p></ng-template></p-card>'
      }
    ];

    templateFixes.forEach(fix => {
      if (fixed.match(fix.from)) {
        fixed = fixed.replace(fix.from, fix.to);
        changes++;
      }
    });

    this.fixes.templateErrors += changes;
    return fixed;
  }

  // Fix import and dialog configuration errors
  fixImportsAndDialogs(content) {
    let fixed = content;
    let changes = 0;

    // Add missing DynamicDialogRef import
    if (fixed.includes('DynamicDialogRef') && !fixed.includes('import { DynamicDialogRef }')) {
      fixed = fixed.replace(
        /(import { DialogService } from 'primeng\/dynamicdialog';)/,
        "import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';"
      );
      changes++;
    }

    // Fix duplicate width properties in dialog configs
    fixed = fixed.replace(
      /width:\s*'[^']*',\s*width:\s*'([^']*)'/g,
      "width: '$1'"
    );

    // Fix maxHeight to height in dialog configs
    fixed = fixed.replace(/maxHeight:/g, 'height:');

    // Fix duplicate DialogService imports
    const dialogImportRegex = /import\s*{\s*DialogService[^}]*}\s*from\s*'primeng\/dynamicdialog';\s*/g;
    const dialogImports = fixed.match(dialogImportRegex);
    if (dialogImports && dialogImports.length > 1) {
      fixed = fixed.replace(dialogImportRegex, '');
      fixed = fixed.replace(
        /(import.*from.*@angular.*;\n)/,
        `$1import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';\n`
      );
      changes++;
    }

    this.fixes.importErrors += changes;
    this.fixes.dialogErrors += changes;
    return fixed;
  }

  // Process a single file
  processFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let fixed = content;

      // Apply all fixes
      fixed = this.fixMessageServiceSyntax(fixed);
      fixed = this.fixTemplateStructures(fixed);
      fixed = this.fixImportsAndDialogs(fixed);

      // Only write if changes were made
      if (fixed !== content) {
        fs.writeFileSync(filePath, fixed, 'utf8');
        console.log(`✅ Fixed: ${filePath}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
      return false;
    }
  }

  // Find all TypeScript and HTML files
  findFiles(dir, extensions = ['.ts', '.html']) {
    const files = [];

    try {
      const items = fs.readdirSync(dir);

      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          files.push(...this.findFiles(fullPath, extensions));
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${dir}:`, error.message);
    }

    return files;
  }

  // Run the final fix
  run() {
    console.log('🔧 Starting Final Fix Script...\n');

    const files = this.findFiles(this.srcPath);
    console.log(`📁 Found ${files.length} files to process\n`);

    let processedFiles = 0;
    let modifiedFiles = 0;

    files.forEach(file => {
      processedFiles++;
      if (this.processFile(file)) {
        modifiedFiles++;
      }
    });

    console.log('\n📊 Final Fix Summary:');
    console.log(`   Files processed: ${processedFiles}`);
    console.log(`   Files modified: ${modifiedFiles}`);
    console.log(`   Syntax errors fixed: ${this.fixes.syntaxErrors}`);
    console.log(`   Template errors fixed: ${this.fixes.templateErrors}`);
    console.log(`   Import errors fixed: ${this.fixes.importErrors}`);
    console.log(`   Dialog errors fixed: ${this.fixes.dialogErrors}`);
    console.log('\n🎯 All critical errors should now be resolved!');
  }
}

// Run the script
if (require.main === module) {
  const fixer = new FinalFixScript();
  fixer.run();
}

module.exports = FinalFixScript;
