#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Final comprehensive template fixes
const comprehensiveFixes = [
  // Fix template structure patterns that are causing the most issues
  { from: '</ng-template>\n      </p-card>', to: '</ng-template>\n      </p-card>' },
  { from: '</form>\n        </ng-template>\n      </p-card>', to: '</form>\n        </ng-template>\n      </p-card>' },
  { from: '</div>\n        </p-tabPanel>', to: '</div>\n        </p-tabPanel>' },
  { from: '</div>\n          </div>\n        </p-tabPanel>', to: '</div>\n          </div>\n        </p-tabPanel>' },
  
  // Fix p-button closing issues
  { from: '</p-button>\n        </p-menu>', to: '</p-button>\n      </p-menu>' },
  { from: '</p-button>\n      </p-menu>', to: '</p-button>\n      </p-menu>' },
  
  // Fix accordion closing issues
  { from: '</p-accordionTab>\n          </p-accordion>', to: '</p-accordionTab>\n        </p-accordion>' },
  
  // Fix table closing issues
  { from: '</table>\n            </div>\n          </p-tabPanel>', to: '</table>\n            </div>\n          </p-tabPanel>' },
  
  // Fix ng-container closing issues
  { from: '</ng-container>\n                </ng-container>', to: '</ng-container>' },
  
  // Fix mat-chip issues
  { from: '</mat-chip-row>\n                  </mat-chip-grid>', to: '</mat-chip-row>\n                </mat-chip-grid>' },
  
  // Fix specific ICU message issues
  { from: "{{ today | date:'MMMM d, yyyy' }", to: "{{ today | date:'MMMM d, yyyy' }}" },
  { from: "{{ userName }", to: "{{ userName }}" }
];

// Specific template structure fixes for problematic patterns
const structuralFixes = [
  // Remove duplicate closing tags
  { pattern: /(<\/ng-template>)\s*(<\/ng-template>)/g, replacement: '$1' },
  { pattern: /(<\/p-card>)\s*(<\/p-card>)/g, replacement: '$1' },
  { pattern: /(<\/p-button>)\s*(<\/p-button>)/g, replacement: '$1' },
  { pattern: /(<\/p-tabPanel>)\s*(<\/p-tabPanel>)/g, replacement: '$1' },
  { pattern: /(<\/form>)\s*(<\/form>)/g, replacement: '$1' },
  { pattern: /(<\/div>)\s*(<\/div>)\s*(<\/div>)/g, replacement: '$1\n          $2' },
  
  // Fix malformed p-button tags
  { pattern: /<p-button>\s*([^>]*?)>/g, replacement: '<p-button $1>' },
  
  // Fix template content structure
  { pattern: /(<ng-template[^>]*pTemplate="content"[^>]*>)\s*(<\/ng-template>)/g, replacement: '$1\n        </ng-template>' },
  
  // Fix accordion structure
  { pattern: /(<p-accordionTab[^>]*>)\s*(<\/p-accordionTab>)/g, replacement: '$1\n          </p-accordionTab>' }
];

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Apply comprehensive fixes
    comprehensiveFixes.forEach(fix => {
      if (content.includes(fix.from)) {
        content = content.replace(new RegExp(fix.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), fix.to);
        modified = true;
      }
    });
    
    // Apply structural fixes
    structuralFixes.forEach(fix => {
      const oldContent = content;
      content = content.replace(fix.pattern, fix.replacement);
      if (content !== oldContent) {
        modified = true;
      }
    });
    
    // Fix specific template issues that are causing the most problems
    const specificPatterns = [
      // Fix unclosed template structures
      { from: '</div></ng-template>', to: '</div>\n        </ng-template>' },
      { from: '</form></p-card>', to: '</form>\n        </ng-template>\n      </p-card>' },
      { from: '</p-button></ng-template>', to: '</p-button>\n        </ng-template>' },
      
      // Fix tab panel structures
      { from: '</div></p-tabPanel>', to: '</div>\n        </p-tabPanel>' },
      { from: '</table></div></p-tabPanel>', to: '</table>\n            </div>\n          </p-tabPanel>' },
      
      // Fix card structures
      { from: '</mat-list></p-card>', to: '</mat-list>\n                  </p-card>' },
      { from: '</mat-list></ng-template></p-card>', to: '</mat-list>\n                  </ng-template>\n                </p-card>' }
    ];
    
    specificPatterns.forEach(pattern => {
      if (content.includes(pattern.from)) {
        content = content.replace(new RegExp(pattern.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), pattern.to);
        modified = true;
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`Fixed: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error.message);
  }
}

function walkDirectory(dir) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      walkDirectory(filePath);
    } else if (file.endsWith('.ts') && !file.endsWith('.spec.ts')) {
      fixFile(filePath);
    }
  });
}

console.log('Starting final comprehensive template fixes...');
walkDirectory('./src');
console.log('Final comprehensive template fixes completed!');
