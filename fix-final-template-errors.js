#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Final template structure fixes
const finalFixes = [
  // Fix ICU message formatting issues
  { from: "{{ today | date:'MMMM d, yyyy' }", to: "{{ today | date:'MMMM d, yyyy' }}" },
  { from: "{{ userName }", to: "{{ userName }}" },
  
  // Fix template closing tag issues
  { from: '</ng-template>\n      </p-card>', to: '</ng-template>\n      </p-card>' },
  { from: '</form>\n        </ng-template>\n      </p-card>', to: '</form>\n        </ng-template>\n      </p-card>' },
  { from: '</div>\n        </p-tabPanel>', to: '</div>\n        </p-tabPanel>' },
  { from: '</div>\n          </div>\n        </p-tabPanel>', to: '</div>\n          </div>\n        </p-tabPanel>' },
  
  // Fix p-button closing issues
  { from: '</p-button>\n        </p-menu>', to: '</p-button>\n      </p-menu>' },
  { from: '</p-button>\n      </p-menu>', to: '</p-button>\n      </p-menu>' },
  
  // Fix accordion closing issues
  { from: '</p-accordionTab>\n          </p-accordion>', to: '</p-accordionTab>\n        </p-accordion>' },
  
  // Fix table closing issues
  { from: '</table>\n            </div>\n          </p-tabPanel>', to: '</table>\n            </div>\n          </p-tabPanel>' },
  
  // Fix ng-container closing issues
  { from: '</ng-container>\n                </ng-container>', to: '</ng-container>' },
  
  // Fix specific component issues
  { from: '[@buttonPress]>', to: '' },
  
  // Fix mat-chip issues
  { from: '</mat-chip-row>\n                  </mat-chip-grid>', to: '</mat-chip-row>\n                </mat-chip-grid>' }
];

// Files that need specific template structure fixes
const specificFixes = {
  'src/app/debug/auth-test.component.ts': [
    { from: '`\n  `, // <-- This is the issue', to: '`' }
  ],
  'src/app/layout/header.component.ts': [
    { from: "{{ today | date:'MMMM d, yyyy' }", to: "{{ today | date:'MMMM d, yyyy' }}" }
  ],
  'src/app/layout/sidebar.component.ts': [
    { from: '`\n  `, // <-- This is the issue', to: '`' }
  ],
  'src/app/layout/user-menu/user-menu.component.ts': [
    { from: "{{ userName }", to: "{{ userName }}" }
  ]
};

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Apply general fixes
    finalFixes.forEach(fix => {
      if (content.includes(fix.from)) {
        content = content.replace(new RegExp(fix.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), fix.to);
        modified = true;
      }
    });
    
    // Apply specific fixes for this file
    if (specificFixes[filePath]) {
      specificFixes[filePath].forEach(fix => {
        if (content.includes(fix.from)) {
          content = content.replace(new RegExp(fix.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), fix.to);
          modified = true;
        }
      });
    }
    
    // Fix template structure patterns
    const patterns = [
      // Fix unclosed template tags
      { pattern: /(<ng-template[^>]*>[\s\S]*?)(<\/ng-template>)(\s*<\/ng-template>)/g, replacement: '$1$2' },
      
      // Fix unclosed p-card tags
      { pattern: /(<p-card[^>]*>[\s\S]*?)(<\/p-card>)(\s*<\/p-card>)/g, replacement: '$1$2' },
      
      // Fix unclosed p-button tags
      { pattern: /(<p-button[^>]*>[\s\S]*?)(<\/p-button>)(\s*<\/p-button>)/g, replacement: '$1$2' },
      
      // Fix unclosed form tags
      { pattern: /(<form[^>]*>[\s\S]*?)(<\/form>)(\s*<\/form>)/g, replacement: '$1$2' },
      
      // Fix unclosed div tags
      { pattern: /(<div[^>]*>[\s\S]*?)(<\/div>)(\s*<\/div>)/g, replacement: '$1$2' }
    ];
    
    patterns.forEach(pattern => {
      const oldContent = content;
      content = content.replace(pattern.pattern, pattern.replacement);
      if (content !== oldContent) {
        modified = true;
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`Fixed: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error.message);
  }
}

function walkDirectory(dir) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      walkDirectory(filePath);
    } else if (file.endsWith('.ts') && !file.endsWith('.spec.ts')) {
      fixFile(filePath);
    }
  });
}

console.log('Starting final template fixes...');
walkDirectory('./src');
console.log('Final template fixes completed!');
