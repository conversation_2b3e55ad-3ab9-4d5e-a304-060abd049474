#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Comprehensive template fixes
const templateFixes = [
  // Fix small tag issues
  { from: '<small class="p-error"', to: '<small class="p-error">' },
  { from: '<small class="p-help"', to: '<small class="p-help">' },

  // Fix p-button tag issues
  { from: '<p-button', to: '<p-button>' },
  { from: 'type="button" p-button', to: 'type="button"' },
  { from: 'button type="button" p-button', to: 'p-button type="button"' },

  // Fix template structure issues
  { from: '</ng-template></p-card>', to: '</ng-template>\n      </p-card>' },
  { from: '</form></p-card>', to: '</form>\n        </ng-template>\n      </p-card>' },
  { from: '</div></p-tabPanel>', to: '</div>\n        </p-tabPanel>' },
  { from: '</table></div></p-tabPanel>', to: '</table>\n            </div>\n          </p-tabPanel>' },

  // Fix mat-list issues
  { from: '</mat-list</ng-template>', to: '</mat-list>\n                  </ng-template>' },
  { from: '</mat-list</p-card>', to: '</mat-list>\n                  </p-card>' },

  // Fix accordion issues
  { from: '</p-accordionTab>', to: '</p-accordionTab>' },
  { from: '</mat-expansion-panel-header>', to: '' },

  // Fix ng-container issues
  { from: '</ng-container></ng-container>', to: '</ng-container>' },

  // Fix specific content issues
  { from: '<p>Content</p></ng-template>', to: '</ng-template>' },
  { from: '<p>Content</p><ng-template pTemplate="content">', to: '</ng-template>\n        <ng-template pTemplate="content">' }
];

// Service syntax fixes
const serviceFixes = [
  { from: '}});', to: '});' }
];

// Import fixes
const importFixes = [
  { from: 'DynamicDialogRef, DynamicDialogConfig', to: 'DynamicDialogRef, DynamicDialogConfig' },
  { from: 'InputTextModule, DropdownModule', to: 'InputTextModule,\n    DropdownModule' }
];

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Apply template fixes
    templateFixes.forEach(fix => {
      if (content.includes(fix.from)) {
        content = content.replace(new RegExp(fix.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), fix.to);
        modified = true;
      }
    });

    // Apply service fixes
    serviceFixes.forEach(fix => {
      if (content.includes(fix.from)) {
        content = content.replace(new RegExp(fix.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), fix.to);
        modified = true;
      }
    });

    // Apply import fixes
    importFixes.forEach(fix => {
      if (content.includes(fix.from)) {
        content = content.replace(new RegExp(fix.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), fix.to);
        modified = true;
      }
    });

    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`Fixed: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error.message);
  }
}

function walkDirectory(dir) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      walkDirectory(filePath);
    } else if (file.endsWith('.ts') && !file.endsWith('.spec.ts')) {
      fixFile(filePath);
    }
  });
}

console.log('Starting comprehensive template fixes...');
walkDirectory('./src');
console.log('Comprehensive template fixes completed!');
