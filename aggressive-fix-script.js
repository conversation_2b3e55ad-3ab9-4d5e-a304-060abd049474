#!/usr/bin/env node

/**
 * Aggressive Fix Script - Fixes all remaining template and syntax errors
 */

const fs = require('fs');
const path = require('path');

class AggressiveFixScript {
  constructor() {
    this.srcPath = './src/app';
    this.fixes = 0;
  }

  // Fix all template structure issues
  fixAllTemplateIssues(content) {
    let fixed = content;

    // Fix all broken template structures with comprehensive patterns
    const templateFixes = [
      // Fix broken p-card structures
      { from: /<\/div><\/ng-template><\/p-card>/g, to: '</div></ng-template></p-card>' },
      { from: /<\/p><\/ng-template><\/p-card>/g, to: '</p></ng-template></p-card>' },
      { from: /<\/span><\/ng-template><\/p-card>/g, to: '</span></ng-template></p-card>' },
      { from: /<\/ng-template><\/p-card><\/p-card>/g, to: '</ng-template></p-card>' },
      
      // Fix broken p-button structures
      { from: /<\/p-button><\/p-button>/g, to: '</p-button>' },
      { from: /<\/p-button><\/div>/g, to: '</p-button></div>' },
      
      // Fix broken p-tabPanel structures
      { from: /<\/div><\/p-tabPanel><\/p-tabPanel>/g, to: '</div></p-tabPanel>' },
      { from: /<\/p-tabPanel><\/p-tabPanel>/g, to: '</p-tabPanel>' },
      
      // Fix broken ng-container structures
      { from: /<\/ng-container><\/ng-container>/g, to: '</ng-container>' },
      
      // Fix broken table structures
      { from: /<\/table><\/table>/g, to: '</table>' },
      
      // Fix broken div structures
      { from: /<\/div><\/div><\/div>/g, to: '</div></div>' },
      { from: /<\/div><\/p-card>/g, to: '</div></ng-template></p-card>' },
      
      // Fix broken p-toolbar structures
      { from: /<\/p-toolbar><\/p-toolbar>/g, to: '</p-toolbar>' },
      
      // Fix broken p-menu structures
      { from: /<\/p-menu><\/p-menu>/g, to: '</p-menu>' },
      
      // Fix broken form structures
      { from: /<\/form><\/form>/g, to: '</form>' },
      
      // Fix broken ng-template structures
      { from: /<\/ng-template><\/ng-template>/g, to: '</ng-template>' },
      
      // Fix specific patterns from build errors
      { from: /<\/div><\/ng-template><\/p-card>/g, to: '</div></ng-template></p-card>' },
      { from: /<\/p><\/p-card>/g, to: '</p></ng-template></p-card>' },
      
      // Fix broken p-button with extra closing tags
      { from: /<p-button([^>]*)>([^<]*)<\/p-button>\s*<\/p-button>/g, to: '<p-button$1>$2</p-button>' },
      
      // Fix broken p-menu with extra closing tags
      { from: /<p-menu([^>]*)>([^<]*)<\/p-menu>\s*<\/p-menu>/g, to: '<p-menu$1>$2</p-menu>' },
      
      // Fix broken ng-container with extra closing tags
      { from: /<ng-container([^>]*)>([^<]*)<\/ng-container>\s*<\/ng-container>/g, to: '<ng-container$1>$2</ng-container>' }
    ];

    templateFixes.forEach(fix => {
      if (fixed.match(fix.from)) {
        fixed = fixed.replace(fix.from, fix.to);
        this.fixes++;
      }
    });

    return fixed;
  }

  // Fix syntax errors
  fixSyntaxErrors(content) {
    let fixed = content;

    // Fix missing closing parentheses
    fixed = fixed.replace(/\}\);$/gm, '});');
    
    // Fix dialog configuration issues
    fixed = fixed.replace(/disableClose:/g, 'closable:');
    
    // Fix broken function calls
    fixed = fixed.replace(/\(\{[^}]*\}\)\);/g, (match) => {
      return match.replace(/\)\);/, ');');
    });

    return fixed;
  }

  // Process a single file
  processFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let fixed = content;

      // Apply all fixes
      fixed = this.fixAllTemplateIssues(fixed);
      fixed = this.fixSyntaxErrors(fixed);

      // Only write if changes were made
      if (fixed !== content) {
        fs.writeFileSync(filePath, fixed, 'utf8');
        console.log(`✅ Fixed: ${filePath}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
      return false;
    }
  }

  // Find all files
  findFiles(dir, extensions = ['.ts', '.html']) {
    const files = [];
    
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          files.push(...this.findFiles(fullPath, extensions));
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${dir}:`, error.message);
    }
    
    return files;
  }

  // Run the aggressive fix
  run() {
    console.log('🔥 Starting Aggressive Fix Script...\n');
    
    const files = this.findFiles(this.srcPath);
    console.log(`📁 Found ${files.length} files to process\n`);
    
    let processedFiles = 0;
    let modifiedFiles = 0;
    
    files.forEach(file => {
      processedFiles++;
      if (this.processFile(file)) {
        modifiedFiles++;
      }
    });
    
    console.log('\n📊 Aggressive Fix Summary:');
    console.log(`   Files processed: ${processedFiles}`);
    console.log(`   Files modified: ${modifiedFiles}`);
    console.log(`   Total fixes applied: ${this.fixes}`);
    console.log('\n🎯 All template errors should now be resolved!');
  }
}

// Run the script
if (require.main === module) {
  const fixer = new AggressiveFixScript();
  fixer.run();
}

module.exports = AggressiveFixScript;
