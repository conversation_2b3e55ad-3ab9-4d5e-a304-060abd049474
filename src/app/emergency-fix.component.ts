import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { InputTextModule } from 'primeng/inputtext';

import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Auth, signInWithEmailAndPassword, signOut, createUserWithEmailAndPassword, updateProfile } from '@angular/fire/auth';
import { Firestore, doc, setDoc, getDoc } from '@angular/fire/firestore';

@Component({
  selector: 'app-emergency-fix',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    CardModule,
    InputTextModule,
    FormsModule
  ],
  template: `
    <div style="padding: 20px; max-width: 600px; margin: 0 auto;">
      <p-card>
        <ng-template pTemplate="header">
          <h3>Emergency Fix</h3>
        </ng-template>

        <ng-template pTemplate="content">
          <!-- Login Section -->
          <div style="margin-bottom: 30px;">
            <h3>1. Login with Existing Account</h3>
            <div class="p-field" appearance="outline" style="width: 100%; margin-bottom: 10px;">
              <label>Email</label>
              <input pInputText [(ngModel)]="email" type="email" value="<EMAIL>">
            </div>
            <div class="p-field" appearance="outline" style="width: 100%; margin-bottom: 10px;">
              <label>Password</label>
              <input pInputText [(ngModel)]="password" type="password">
            </div>
            <p-button severity="primary" (click)="emergencyLogin()" [disabled]="loading">
              {{ loading ? 'Logging in...' : 'Emergency Login' }}
            </p-button>
          </div>

          <!-- Profile Fix Section -->
          <div style="margin-bottom: 30px;">
            <h3>2. Fix Missing Profile</h3>
            <p-button color="warn" (click)="fixProfile()" [disabled]="loading">
              {{ loading ? 'Fixing...' : 'Create Missing Profile' }}
            </p-button>
          </div>

          <!-- Sign Out Section -->
          <div style="margin-bottom: 30px;">
            <h3>3. Sign Out</h3>
            <p-button (click)="emergencySignOut()" [disabled]="loading">
              {{ loading ? 'Signing out...' : 'Emergency Sign Out' }}
            </p-button>
          </div>

          <!-- Status Display -->
          <div *ngIf="messages.length > 0" style="margin-top: 20px;">
            <h4>Status Messages:</h4>
            <div *ngFor="let msg of messages"
                 [style.color]="msg.type === 'error' ? 'red' : msg.type === 'success' ? 'green' : 'blue'"
                 style="margin: 5px 0; padding: 5px; border-left: 3px solid; padding-left: 10px;">
              <strong>{{ msg.timestamp }}</strong>: {{ msg.message }}
            </div>
        </ng-template>
      </p-card>
    </div>
  `
})
export class EmergencyFixComponent {
  private auth = inject(Auth);
  private firestore = inject(Firestore);
  private router = inject(Router);

  email = '<EMAIL>';
  password = '';
  loading = false;
  messages: Array<{type: string, message: string, timestamp: string}> = [];

  async emergencyLogin() {
    this.loading = true;
    this.addMessage('info', 'Attempting emergency login...');

    try {
      const credential = await signInWithEmailAndPassword(this.auth, this.email, this.password);
      this.addMessage('success', `Login successful: ${credential.user.email}`);

      // Check if profile exists
      const userDoc = doc(this.firestore, `users/${credential.user.uid}`);
      const docSnap = await getDoc(userDoc);

      if (docSnap.exists()) {
        this.addMessage('success', 'User profile found in Firestore');
        this.addMessage('info', 'Redirecting to dashboard...');
        setTimeout(() => {
          this.router.navigate(['/dashboard']);
        }, 2000);
      } else {
        this.addMessage('error', 'User profile NOT found - use "Create Missing Profile" button');
      }
    } catch (error: any) {
      this.addMessage('error', `Login failed: ${error.message}`);
    }

    this.loading = false;
  }

  async fixProfile() {
    this.loading = true;
    this.addMessage('info', 'Attempting to create missing profile...');

    try {
      const user = this.auth.currentUser;
      if (!user) {
        this.addMessage('error', 'No authenticated user found. Login first.');
        this.loading = false;
        return;
      }

      // Create user profile
      const userProfile = {
        uid: user.uid,
        email: user.email!,
        displayName: user.displayName || 'User',
        role: 'admin',
        businessIds: [],
        primaryBusinessId: '',
        createdAt: new Date(),
        lastLoginAt: new Date()
      };

      const userDoc = doc(this.firestore, `users/${user.uid}`);
      await setDoc(userDoc, userProfile);

      this.addMessage('success', 'User profile created successfully!');
      this.addMessage('info', 'Profile data saved to Firestore');
      this.addMessage('info', 'You can now access all features');

    } catch (error: any) {
      this.addMessage('error', `Profile creation failed: ${error.message}`);
    }

    this.loading = false;
  }

  async emergencySignOut() {
    this.loading = true;
    this.addMessage('info', 'Attempting sign out...');

    try {
      await signOut(this.auth);
      this.addMessage('success', 'Sign out successful');
      this.addMessage('info', 'Redirecting to login...');
      setTimeout(() => {
        this.router.navigate(['/auth/login']);
      }, 2000);
    } catch (error: any) {
      this.addMessage('error', `Sign out failed: ${error.message}`);
    }

    this.loading = false;
  }

  private addMessage(type: string, message: string) {
    const timestamp = new Date().toLocaleTimeString();
    this.messages.unshift({ type, message, timestamp });
    console.log(`🚨 Emergency Fix [${type.toUpperCase()}]:`, message);
  }
}
