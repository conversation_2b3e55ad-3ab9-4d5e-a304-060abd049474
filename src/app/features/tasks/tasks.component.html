<div class="tasks-container">
  <!-- Header Section -->
  <div class="tasks-header">
    <div class="header-content">
      <div class="title-section">
        <h1>
          <i class="pi pi-tasks"></i>
          Tasks & Checklists
        </h1>
        <p class="subtitle">Manage tasks, checklists, and team productivity with AI assistance</p>
      </div>

      <div class="header-actions">
        <p-button severity="primary" (click)="createTask()">
          <i class="pi pi-plus"></i>
          Create Task
        </p-button>
        <p-button severity="secondary" (click)="createChecklist()">
          <i class="pi pi-list"></i>
          Create Checklist
        </p-button>
        <p-button outlined="true" severity="primary" (click)="getAITaskSuggestions()">
          <i class="pi pi-brain"></i>
          AI Suggestions
        </p-button>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="filters-section">
      <span class="p-input-icon-left search-field">
        <i class="pi pi-search"></i>
        <input pInputText [(ngModel)]="searchText" placeholder="Search by title, description, or assignee" class="w-full">
      </span>

      <p-multiSelect [options]="statusOptions" [(ngModel)]="selectedStatuses"
                     placeholder="Filter by Status"
                     optionLabel="label" optionValue="value" class="filter-dropdown">
      </p-multiSelect>

      <p-multiSelect [options]="priorityOptions" [(ngModel)]="selectedPriorities"
                     placeholder="Filter by Priority"
                     optionLabel="label" optionValue="value" class="filter-dropdown">
      </p-multiSelect>

      <p-multiSelect [options]="categoryOptions" [(ngModel)]="selectedCategories"
                     placeholder="Filter by Category"
                     optionLabel="label" optionValue="value" class="filter-dropdown">
      </p-multiSelect>

      <p-button icon="pi pi-filter-slash" (click)="clearFilters()"
                pTooltip="Clear all filters" tooltipPosition="top"
                severity="secondary" [text]="true">
      </p-button>
    </div>
  </div>

  <!-- Loading Indicator -->
  <p-progressBar *ngIf="isLoading()" mode="indeterminate" class="loading-bar"></p-progressBar>

  <!-- Main Content Tabs -->
  <p-tabView [(activeIndex)]="selectedTab" class="tasks-tabs">

    <!-- Tasks Tab -->
    <p-tabPanel header="Tasks">
      <div class="tab-content">

        <!-- Tasks Summary Cards -->
        <div class="summary-cards" *ngIf="tasks$ | async as tasks">
          <p-card class="summary-card">
            <ng-template pTemplate="content">
              <div class="summary-content">
                <i class="pi pi-clock"></i>
                <div class="summary-text">
                  <h3>{{ getTasksByStatus(tasks, 'pending').length }}</h3>
                  <p>Pending Tasks</p>
                </div>
              </div>
            </ng-template>
          </p-card>

          <p-card class="summary-card">
            <ng-template pTemplate="content">
              <div class="summary-content">
                <i class="pi pi-play"></i>
                <div class="summary-text">
                  <h3>{{ getTasksByStatus(tasks, 'in-progress').length }}</h3>
                  <p>In Progress</p>
                </div>
              </div>
            </ng-template>
          </p-card>

          <p-card class="summary-card">
            <ng-template pTemplate="content">
              <div class="summary-content">
                <i class="pi pi-check"></i>
                <div class="summary-text">
                  <h3>{{ getTasksByStatus(tasks, 'completed').length }}</h3>
                  <p>Completed</p>
                </div>
              </div>
            </ng-template>
          </p-card>

          <p-card class="summary-card">
            <ng-template pTemplate="content">
              <div class="summary-content">
                <i class="pi pi-exclamation-triangle"></i>
                <div class="summary-text">
                  <h3>{{ getTasksByStatus(tasks, 'overdue').length }}</h3>
                  <p>Overdue</p>
                </div>
              </div>
            </ng-template>
          </p-card>
        </div>

        <!-- Tasks Table -->
        <p-card class="tasks-table-card">
          <ng-template pTemplate="header">
            <div class="card-header">
              <h3>
                <i class="pi pi-tasks"></i>
                All Tasks
              </h3>
              <div class="table-actions">
                <p-button icon="pi pi-sort" [text]="true"
                          pTooltip="Sort options" tooltipPosition="top"
                          (click)="sortMenu.toggle($event)">
                </p-button>
                <p-menu #sortMenu [popup]="true" [model]="sortMenuItems"></p-menu>
              </div>
            </div>
          </ng-template>

          <ng-template pTemplate="content">
            <div class="table-container">
              <p-table [value]="(tasks$ | async) || []"
                       [paginator]="true" [rows]="10" [showCurrentPageReport]="true"
                       currentPageReportTemplate="Showing {first} to {last} of {totalRecords} tasks"
                       [rowsPerPageOptions]="[10, 25, 50]"
                       class="tasks-table">

                <ng-template pTemplate="header">
                  <tr>
                    <th>Task</th>
                    <th>Status</th>
                    <th>Priority</th>
                    <th>Assigned To</th>
                    <th>Due Date</th>
                    <th>Progress</th>
                    <th>Actions</th>
                  </tr>
                </ng-template>

                <ng-template pTemplate="body" let-task>
                  <tr>
                    <td>
                      <div class="task-title-cell">
                        <h4>{{ task.title }}</h4>
                        <p *ngIf="task.description">{{ task.description | slice:0:100 }}{{ task.description.length > 100 ? '...' : '' }}</p>
                        <div class="task-tags" *ngIf="task.tags && task.tags.length > 0">
                          <p-chip *ngFor="let tag of task.tags | slice:0:3" class="task-tag">{{ tag }}</p-chip>
                          <span *ngIf="task.tags.length > 3" class="more-tags">+{{ task.tags.length - 3 }} more</span>
                        </div>
                      </div>
                    </td>
                    <td>
                      <p-tag [value]="task.status | titlecase"
                             [severity]="getStatusSeverity(task.status)">
                      </p-tag>
                    </td>
                    <td>
                      <p-tag [value]="task.priority | titlecase"
                             [severity]="getPrioritySeverity(task.priority)">
                      </p-tag>
                    </td>
                    <td>
                      <div class="assignees">
                        {{ formatStaffNames(task.assignedTo) | async }}
                      </div>
                    </td>
                    <td>
                      <div class="due-date" [class.overdue]="isTaskOverdue(task)">
                        <i class="pi pi-calendar"></i>
                        {{ task.dueDate ? (task.dueDate | date:'mediumDate') : 'No due date' }}
                      </div>
                    </td>
                    <td>
                      <div class="progress-cell">
                        <p-progressBar [value]="task.progress" [showValue]="false"></p-progressBar>
                        <span class="progress-text">{{ task.progress }}%</span>
                      </div>
                    </td>
                    <td>
                      <p-button icon="pi pi-ellipsis-v" [text]="true"
                                pTooltip="Task actions" tooltipPosition="top"
                                (click)="taskMenu.toggle($event)"
                                [attr.data-task-id]="task.id">
                      </p-button>
                      <p-menu #taskMenu [popup]="true" [model]="getTaskMenuItems(task)"></p-menu>
                    </td>
                  </tr>
                </ng-template>

                <ng-template pTemplate="emptymessage">
                  <tr>
                    <td colspan="7" class="text-center">
                      <div class="empty-state">
                        <i class="pi pi-inbox"></i>
                        <h3>No tasks found</h3>
                        <p>Create your first task or adjust your filters to see results.</p>
                        <p-button severity="primary" (click)="createTask()">
                          <i class="pi pi-plus"></i>
                          Create Task
                        </p-button>
                      </div>
                    </td>
                  </tr>
                </ng-template>

              </p-table>
            </div>
          </ng-template>
        </p-card>
      </div>
    </p-tabPanel>

    <!-- Checklists Tab -->
    <p-tabPanel header="Checklists">
      <div class="tab-content">
        <!-- Checklists Table -->
        <p-card class="checklists-table-card">
          <ng-template pTemplate="header">
            <div class="card-header">
              <h3>
                <i class="pi pi-list"></i>
                All Checklists
              </h3>
            </div>
          </ng-template>

          <ng-template pTemplate="content">
            <div class="table-container">
              <p-table [value]="(checklists$ | async) || []"
                       [paginator]="true" [rows]="10" [showCurrentPageReport]="true"
                       currentPageReportTemplate="Showing {first} to {last} of {totalRecords} checklists"
                       [rowsPerPageOptions]="[10, 25, 50]"
                       class="checklists-table">

                <ng-template pTemplate="header">
                  <tr>
                    <th>Checklist</th>
                    <th>Status</th>
                    <th>Priority</th>
                    <th>Assigned To</th>
                    <th>Due Date</th>
                    <th>Completion</th>
                    <th>Actions</th>
                  </tr>
                </ng-template>

                <ng-template pTemplate="body" let-checklist>
                  <tr>
                    <td>
                      <div class="checklist-title-cell">
                        <h4>{{ checklist.title }}</h4>
                        <p *ngIf="checklist.description">{{ checklist.description | slice:0:100 }}{{ checklist.description.length > 100 ? '...' : '' }}</p>
                        <div class="checklist-info">
                          <span class="item-count">{{ checklist.items?.length || 0 }} items</span>
                        </div>
                      </div>
                    </td>
                    <td>
                      <p-tag [value]="checklist.status | titlecase"
                             [severity]="getStatusSeverity(checklist.status)">
                      </p-tag>
                    </td>
                    <td>
                      <p-tag [value]="checklist.priority | titlecase"
                             [severity]="getPrioritySeverity(checklist.priority)">
                      </p-tag>
                    </td>
                    <td>
                      <div class="assignees">
                        {{ formatStaffNames(checklist.assignedTo) | async }}
                      </div>
                    </td>
                    <td>
                      <div class="due-date" [class.overdue]="isChecklistOverdue(checklist)">
                        <i class="pi pi-calendar"></i>
                        {{ checklist.dueDate ? (checklist.dueDate | date:'mediumDate') : 'No due date' }}
                      </div>
                    </td>
                    <td>
                      <div class="completion-cell">
                        <p-progressBar [value]="checklist.completionPercentage" [showValue]="false"></p-progressBar>
                        <span class="completion-text">{{ checklist.completionPercentage }}%</span>
                      </div>
                    </td>
                    <td>
                      <p-button icon="pi pi-ellipsis-v" [text]="true"
                                pTooltip="Checklist actions" tooltipPosition="top"
                                (click)="checklistMenu.toggle($event)"
                                [attr.data-checklist-id]="checklist.id">
                      </p-button>
                      <p-menu #checklistMenu [popup]="true" [model]="getChecklistMenuItems(checklist)"></p-menu>
                    </td>
                  </tr>
                </ng-template>

                <ng-template pTemplate="emptymessage">
                  <tr>
                    <td colspan="7" class="text-center">
                      <div class="empty-state">
                        <i class="pi pi-inbox"></i>
                        <h3>No checklists found</h3>
                        <p>Create your first checklist or adjust your filters to see results.</p>
                        <p-button severity="secondary" (click)="createChecklist()">
                          <i class="pi pi-list"></i>
                          Create Checklist
                        </p-button>
                      </div>
                    </td>
                  </tr>
                </ng-template>

              </p-table>
            </div>
          </ng-template>
        </p-card>
      </div>
    </p-tabPanel>

    <!-- Analytics Tab -->
    <p-tabPanel header="Analytics">
      <div class="tab-content">
        <div class="analytics-section">
          <p-card class="analytics-card">
            <ng-template pTemplate="header">
              <div class="card-header">
                <h3>
                  <i class="pi pi-chart-bar"></i>
                  Productivity Analytics
                </h3>
                <div class="analytics-actions">
                  <p-button severity="primary" (click)="analyzeProductivity()">
                    <i class="pi pi-brain"></i>
                    AI Analysis
                  </p-button>
                </div>
              </div>
            </ng-template>

            <ng-template pTemplate="content">
              <p>AI-powered productivity insights and recommendations will be displayed here.</p>
              <p>Click "AI Analysis" to generate personalized productivity recommendations.</p>
            </ng-template>
          </p-card>
        </div>
      </div>
    </p-tabPanel>

  </p-tabView>
</div>
