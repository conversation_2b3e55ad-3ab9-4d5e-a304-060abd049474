import { Component, OnInit, inject, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Observable, combineLatest, map, startWith } from 'rxjs';

// Angular Material Imports
import { CardModule } from 'primeng/card';
import { DialogService } from 'primeng/dynamicdialog';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';

import { TabViewModule } from 'primeng/tabview';
import { TableModule } from 'primeng/table';
import { PaginatorModule } from 'primeng/paginator';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';

import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { MultiSelectModule } from 'primeng/multiselect';
import { ChipModule } from 'primeng/chip';
import { BadgeModule } from 'primeng/badge';
import { MenuModule } from 'primeng/menu';

import { ToastModule } from 'primeng/toast';
import { ProgressBarModule } from 'primeng/progressbar';
import { CheckboxModule } from 'primeng/checkbox';
import { CalendarModule } from 'primeng/calendar';

import { ToolbarModule } from 'primeng/toolbar';
import { DividerModule } from 'primeng/divider';
import { ListboxModule } from 'primeng/listbox';
import { AccordionModule } from 'primeng/accordion';

// Services
import { TaskManagementService } from './services/task-management.service';
import { AITaskAssistantService } from './services/ai-task-assistant.service';
import { AuthService } from '../../core/auth/auth.service';
import { StaffFirestoreService } from '../staff/services/staff-firestore.service';

// Models
import { Task, Checklist, TaskFilter, TaskSortOptions } from './models/task.model';
import { StaffMember } from '../staff/models/staff.model';

// Components
import { TaskDialogComponent, TaskDialogData } from './components/task-dialog.component';
import { ChecklistDialogComponent, ChecklistDialogData } from './components/checklist-dialog.component';

@Component({
  selector: 'app-tasks',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    CardModule,
    ButtonModule,
    TabViewModule,
    TableModule,
    PaginatorModule,
    TagModule,
    TooltipModule,
    InputTextModule,
    DropdownModule,
    MultiSelectModule,
    ChipModule,
    BadgeModule,
    MenuModule,
    ToastModule,
    ProgressBarModule,
    CheckboxModule,
    CalendarModule,
    ToolbarModule,
    DividerModule,
    ListboxModule,
    AccordionModule
  ],
  templateUrl: './tasks.component.html',
  styleUrls: ['./tasks.component.scss']
})
export class TasksComponent implements OnInit {
  // Injected services
  private taskService = inject(TaskManagementService);
  private aiService = inject(AITaskAssistantService);
  private authService = inject(AuthService);
  private staffService = inject(StaffFirestoreService);
  private dialog = inject(DialogService);
  private messageService = inject(MessageService);
  private router = inject(Router);

  // Signals for reactive state management
  selectedTab = signal(0);
  isLoading = signal(false);
  searchText = signal('');
  selectedStatuses = signal<string[]>([]);
  selectedPriorities = signal<string[]>([]);
  selectedCategories = signal<string[]>([]);
  selectedFilters = signal<TaskFilter>({});
  sortOptions = signal<TaskSortOptions>({ field: 'createdAt', direction: 'desc' });

  // Data observables
  tasks$!: Observable<Task[]>;
  checklists$!: Observable<Checklist[]>;
  staff$!: Observable<StaffMember[]>;
  currentUser$ = this.authService.userProfile$;

  // Computed values
  filteredTasks$ = computed(() => {
    // This will be implemented with proper filtering logic
    return this.tasks$;
  });

  // Table configuration
  taskColumns = ['title', 'status', 'priority', 'assignedTo', 'dueDate', 'progress', 'actions'];
  checklistColumns = ['title', 'status', 'priority', 'assignedTo', 'dueDate', 'completion', 'actions'];

  // Filter options
  statusOptions = [
    { value: 'pending', label: 'Pending', color: 'warn' },
    { value: 'in-progress', label: 'In Progress', color: 'primary' },
    { value: 'completed', label: 'Completed', color: 'accent' },
    { value: 'on-hold', label: 'On Hold', color: 'basic' },
    { value: 'cancelled', label: 'Cancelled', color: 'basic' },
    { value: 'overdue', label: 'Overdue', color: 'warn' }
  ];

  priorityOptions = [
    { value: 'low', label: 'Low', color: 'basic' },
    { value: 'medium', label: 'Medium', color: 'primary' },
    { value: 'high', label: 'High', color: 'accent' },
    { value: 'urgent', label: 'Urgent', color: 'warn' },
    { value: 'critical', label: 'Critical', color: 'warn' }
  ];

  categoryOptions = [
    { value: 'administrative', label: 'Administrative' },
    { value: 'customer-service', label: 'Customer Service' },
    { value: 'training', label: 'Training' },
    { value: 'maintenance', label: 'Maintenance' },
    { value: 'project', label: 'Project' },
    { value: 'safety', label: 'Safety' },
    { value: 'compliance', label: 'Compliance' },
    { value: 'custom', label: 'Custom' }
  ];

  ngOnInit(): void {
    this.initializeData();
  }

  private initializeData(): void {
    this.isLoading.set(true);

    // Get current user's business context
    this.currentUser$.subscribe(user => {
      if (user?.primaryBusinessId) {
        this.loadTasksAndChecklists(user.primaryBusinessId);
        this.loadStaff(user.primaryBusinessId);
      }
    });
  }

  private loadTasksAndChecklists(businessId: string): void {
    const filter: TaskFilter = {
      businessId,
      ...this.selectedFilters()
    };

    this.tasks$ = this.taskService.getTasks(filter, this.sortOptions());
    this.checklists$ = this.taskService.getChecklists(filter, this.sortOptions());

    // Subscribe to data loading completion
    combineLatest([this.tasks$, this.checklists$]).subscribe(() => {
      this.isLoading.set(false);
    });
  }

  private loadStaff(businessId: string): void {
    this.staff$ = this.staffService.getActiveStaff(businessId);
  }

  // ==================== TASK ACTIONS ====================

  createTask(): void {
    // Open task creation dialog
    this.openTaskDialog();
  }

  createChecklist(): void {
    // Open checklist creation dialog
    this.openChecklistDialog();
  }

  editTask(task: Task): void {
    this.openTaskDialog(task);
  }

  editChecklist(checklist: Checklist): void {
    this.openChecklistDialog(checklist);
  }

  deleteTask(task: Task): void {
    if (confirm(`Are you sure you want to delete "${task.title}"?`)) {
      this.taskService.deleteTask(task.id).subscribe({
        next: () => {
          this.messageService.add({ severity: 'info', summary: 'Task deleted successfully' });
          this.refreshData();
        },
        error: (error) => {
          this.messageService.add({ severity: 'info', summary: 'Error deleting task' });
          console.error('Delete task error:', error);
        }
      });
    }
  }

  deleteChecklist(checklist: Checklist): void {
    if (confirm(`Are you sure you want to delete "${checklist.title}"?`)) {
      this.taskService.deleteChecklist(checklist.id).subscribe({
        next: () => {
          this.messageService.add({ severity: 'info', summary: 'Checklist deleted successfully' });
          this.refreshData();
        },
        error: (error) => {
          this.messageService.add({ severity: 'info', summary: 'Error deleting checklist' });
          console.error('Delete checklist error:', error);
        }
      });
    }
  }

  // ==================== AI ASSISTANCE ====================

  async getAITaskSuggestions(): Promise<void> {
    this.isLoading.set(true);

    try {
      const user = await this.currentUser$.pipe(map(u => u)).toPromise();
      if (user?.staffId && user?.primaryBusinessId) {
        const suggestions = await this.aiService.generateTaskSuggestions(
          user.staffId,
          user.primaryBusinessId,
          'General task suggestions for productivity improvement'
        );

        // Show AI suggestions dialog
        this.showAISuggestionsDialog(suggestions);
      }
    } catch (error) {
      this.messageService.add({ severity: 'info', summary: 'Error getting AI suggestions' });
      console.error('AI suggestions error:', error);
    } finally {
      this.isLoading.set(false);
    }
  }

  async analyzeProductivity(): Promise<void> {
    this.isLoading.set(true);

    try {
      const user = await this.currentUser$.pipe(map(u => u)).toPromise();
      if (user?.staffId) {
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 30); // Last 30 days

        const analysis = await this.aiService.analyzeStaffProductivity(user.staffId, {
          start: startDate,
          end: endDate
        });

        // Show productivity analysis dialog
        this.showProductivityAnalysisDialog(analysis);
      }
    } catch (error) {
      this.messageService.add({ severity: 'info', summary: 'Error analyzing productivity' });
      console.error('Productivity analysis error:', error);
    } finally {
      this.isLoading.set(false);
    }
  }

  // ==================== FILTERING AND SORTING ====================

  applyFilters(filters: TaskFilter): void {
    this.selectedFilters.set(filters);
    this.refreshData();
  }

  applySorting(sort: TaskSortOptions): void {
    this.sortOptions.set(sort);
    this.refreshData();
  }

  clearFilters(): void {
    this.selectedFilters.set({});
    this.searchText.set('');
    this.refreshData();
  }

  // ==================== UTILITY METHODS ====================

  private refreshData(): void {
    this.currentUser$.subscribe(user => {
      if (user?.primaryBusinessId) {
        this.loadTasksAndChecklists(user.primaryBusinessId);
      }
    });
  }

  private openTaskDialog(task?: Task): void {
    this.currentUser$.subscribe(user => {
      if (!user?.primaryBusinessId) return;

      const dialogData: TaskDialogData = {
        task,
        businessId: user.primaryBusinessId,
        mode: task ? 'edit' : 'create'
      };

      const dialogRef = this.dialog.open(TaskDialogComponent, {
        width: '90vw',
        height: '90vh',
        data: dialogData,
        closable: false
      });

      dialogRef.onClose.subscribe(result => {
        if (result) {
          this.refreshData();
        }
      });
    });
  }

  private openChecklistDialog(checklist?: Checklist): void {
    this.currentUser$.subscribe(user => {
      if (!user?.primaryBusinessId) return;

      const dialogData: ChecklistDialogData = {
        checklist,
        businessId: user.primaryBusinessId,
        mode: checklist ? 'edit' : 'create'
      };

      const dialogRef = this.dialog.open(ChecklistDialogComponent, {
        width: '95vw',
        height: '90vh',
        data: dialogData,
        closable: false
      });

      dialogRef.onClose.subscribe(result => {
        if (result) {
          this.refreshData();
        }
      });
    });
  }

  private showAISuggestionsDialog(suggestions: any[]): void {
    // Implementation for AI suggestions dialog will be added
    console.log('AI suggestions:', suggestions);
  }

  private showProductivityAnalysisDialog(analysis: any): void {
    // Implementation for productivity analysis dialog will be added
    console.log('Productivity analysis:', analysis);
  }

  getStatusColor(status: string): string {
    const statusOption = this.statusOptions.find(option => option.value === status);
    return statusOption?.color || 'basic';
  }

  getPriorityColor(priority: string): string {
    const priorityOption = this.priorityOptions.find(option => option.value === priority);
    return priorityOption?.color || 'basic';
  }

  formatStaffNames(staffIds: string[]): Observable<string> {
    return this.staff$.pipe(
      map(staff => {
        const names = staffIds.map(id => {
          const member = staff.find(s => s.id === id);
          return member ? `${member.firstName} ${member.lastName}` : 'Unknown';
        });
        return names.join(', ');
      })
    );
  }

  // Helper methods for template
  getTasksByStatus(tasks: Task[], status: string): Task[] {
    return tasks.filter(task => task.status === status);
  }

  getChecklistsByStatus(checklists: Checklist[], status: string): Checklist[] {
    return checklists.filter(checklist => checklist.status === status);
  }

  isTaskOverdue(task: Task): boolean {
    return !!(task.dueDate && task.dueDate < new Date() && task.status !== 'completed');
  }

  isChecklistOverdue(checklist: Checklist): boolean {
    return !!(checklist.dueDate && checklist.dueDate < new Date() && checklist.status !== 'completed');
  }

  getTasksDataSource(): Task[] {
    return [];
  }

  getChecklistsDataSource(): Checklist[] {
    return [];
  }

  // PrimeNG severity mapping methods
  getStatusSeverity(status: string): 'success' | 'info' | 'warning' | 'danger' | 'secondary' | 'contrast' | undefined {
    switch (status) {
      case 'completed': return 'success';
      case 'in-progress': return 'info';
      case 'pending': return 'warning';
      case 'overdue': return 'danger';
      case 'on-hold': return 'secondary';
      case 'cancelled': return 'contrast';
      default: return 'secondary';
    }
  }

  getPrioritySeverity(priority: string): 'success' | 'info' | 'warning' | 'danger' | 'secondary' | 'contrast' | undefined {
    switch (priority) {
      case 'low': return 'secondary';
      case 'medium': return 'info';
      case 'high': return 'warning';
      case 'urgent': return 'danger';
      case 'critical': return 'danger';
      default: return 'secondary';
    }
  }

  // Menu items for actions
  get sortMenuItems() {
    return [
      {
        label: 'Title A-Z',
        icon: 'pi pi-sort-alpha-down',
        command: () => this.applySorting({ field: 'title', direction: 'asc' })
      },
      {
        label: 'Due Date',
        icon: 'pi pi-calendar',
        command: () => this.applySorting({ field: 'dueDate', direction: 'asc' })
      },
      {
        label: 'Priority',
        icon: 'pi pi-exclamation-triangle',
        command: () => this.applySorting({ field: 'priority', direction: 'desc' })
      },
      {
        label: 'Created Date',
        icon: 'pi pi-clock',
        command: () => this.applySorting({ field: 'createdAt', direction: 'desc' })
      }
    ];
  }

  getTaskMenuItems(task: Task) {
    return [
      {
        label: 'Edit',
        icon: 'pi pi-pencil',
        command: () => this.editTask(task)
      },
      {
        label: 'View Details',
        icon: 'pi pi-eye',
        command: () => this.viewTaskDetails(task)
      },
      {
        label: 'Duplicate',
        icon: 'pi pi-copy',
        command: () => this.duplicateTask(task)
      },
      {
        separator: true
      },
      {
        label: 'Delete',
        icon: 'pi pi-trash',
        styleClass: 'text-red-500',
        command: () => this.deleteTask(task)
      }
    ];
  }

  getChecklistMenuItems(checklist: Checklist) {
    return [
      {
        label: 'Edit',
        icon: 'pi pi-pencil',
        command: () => this.editChecklist(checklist)
      },
      {
        label: 'View Details',
        icon: 'pi pi-eye',
        command: () => this.viewChecklistDetails(checklist)
      },
      {
        label: 'Duplicate',
        icon: 'pi pi-copy',
        command: () => this.duplicateChecklist(checklist)
      },
      {
        separator: true
      },
      {
        label: 'Delete',
        icon: 'pi pi-trash',
        styleClass: 'text-red-500',
        command: () => this.deleteChecklist(checklist)
      }
    ];
  }

  // Additional action methods
  viewTaskDetails(task: Task): void {
    // Implementation for viewing task details
    console.log('View task details:', task);
  }

  viewChecklistDetails(checklist: Checklist): void {
    // Implementation for viewing checklist details
    console.log('View checklist details:', checklist);
  }

  duplicateTask(task: Task): void {
    // Implementation for duplicating task
    console.log('Duplicate task:', task);
  }

  duplicateChecklist(checklist: Checklist): void {
    // Implementation for duplicating checklist
    console.log('Duplicate checklist:', checklist);
  }
}
