import { Injectable, inject } from '@angular/core';
import { Observable, from, map, combineLatest, of } from 'rxjs';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { environment } from '../../../../environments/environment';
import { TaskManagementService } from './task-management.service';
import { StaffFirestoreService } from '../../staff/services/staff-firestore.service';
import { CalendarService } from '../../calendar/services/calendar.service';
import {
  Task,
  Checklist,
  ChecklistItem,
  AISuggestion,
  TaskAnalytics,
  TaskTemplate,
  AIInsight
} from '../models/task.model';
import { StaffMember } from '../../staff/models/staff.model';
import { CalendarEvent } from '../../calendar/models/calendar.model';

export interface AITaskSuggestion {
  type: 'task-creation' | 'checklist-creation' | 'optimization' | 'scheduling' | 'resource-allocation';
  title: string;
  description: string;
  suggestedData?: Partial<Task | Checklist>;
  reasoning: string;
  confidence: number;
  priority: 'low' | 'medium' | 'high';
  estimatedImpact: string;
}

export interface AIProductivityAnalysis {
  staffId: string;
  staffName: string;
  period: { start: Date; end: Date };
  metrics: {
    tasksCompleted: number;
    averageCompletionTime: number;
    overdueRate: number;
    productivityScore: number;
  };
  insights: AIInsight[];
  recommendations: string[];
}

export interface AIWorkloadAnalysis {
  businessId: string;
  period: { start: Date; end: Date };
  overallMetrics: {
    totalTasks: number;
    completionRate: number;
    averageTaskDuration: number;
    workloadDistribution: { [staffId: string]: number };
  };
  staffAnalysis: AIProductivityAnalysis[];
  recommendations: AITaskSuggestion[];
  optimizationOpportunities: string[];
}

@Injectable({
  providedIn: 'root'
})
export class AITaskAssistantService {
  private taskService = inject(TaskManagementService);
  private staffService = inject(StaffFirestoreService);
  private calendarService = inject(CalendarService);
  private genAI: GoogleGenerativeAI;

  constructor() {
    this.genAI = new GoogleGenerativeAI(environment.googleAI.apiKey);
  }

  // ==================== TASK GENERATION AND SUGGESTIONS ====================

  /**
   * Generate task suggestions based on staff goals and context
   */
  async generateTaskSuggestions(
    staffId: string,
    businessId: string,
    context?: string
  ): Promise<AITaskSuggestion[]> {
    try {
      const model = this.genAI.getGenerativeModel({
        model: "gemini-2.5-flash-preview-04-17",
        generationConfig: {
          temperature: 0.7,
          topP: 0.8,
          topK: 40,
          maxOutputTokens: 2048,
        }
      });

      // Get staff data and current tasks for context
      const staff = await this.staffService.getStaffById(staffId).toPromise();
      const currentTasks = await this.taskService.getStaffTasks(staffId, false).toPromise();

      const prompt = `
        As an expert task management consultant, analyze the following staff member's profile and suggest relevant tasks:

        Staff Profile:
        - Name: ${staff?.firstName} ${staff?.lastName}
        - Position: ${staff?.position}
        - Department: ${staff?.department}
        - Skills: ${staff?.skills?.map((s: any) => s.name).join(', ') || 'None specified'}
        - Current Goals: None specified (goals managed separately)

        Current Active Tasks: ${currentTasks?.length || 0}
        ${currentTasks?.map(t => `- ${t.title} (${t.status}, Priority: ${t.priority})`).join('\n') || 'No active tasks'}

        Additional Context: ${context || 'None provided'}

        Please suggest 3-5 relevant tasks that would help this staff member:
        1. Achieve their goals
        2. Develop their skills
        3. Contribute to business objectives
        4. Maintain work-life balance

        For each suggestion, provide:
        - Task title and description
        - Recommended priority level
        - Estimated duration
        - Skills/goals it addresses
        - Why it's beneficial

        Format your response as a JSON array of task suggestions.
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      // Parse AI response and convert to AITaskSuggestion format
      return this.parseTaskSuggestions(text);
    } catch (error) {
      console.error('Error generating task suggestions:', error);
      return [];
    }
  }

  /**
   * Generate checklist templates based on task category and context
   */
  async generateChecklistTemplate(
    category: string,
    context: string,
    businessType?: string
  ): Promise<ChecklistItem[]> {
    try {
      const model = this.genAI.getGenerativeModel({
        model: "gemini-2.5-flash-preview-04-17",
        generationConfig: {
          temperature: 0.6,
          topP: 0.9,
          maxOutputTokens: 2000,
        }
      });

      const prompt = `
        Create a comprehensive checklist for the following scenario:

        Category: ${category}
        Context: ${context}
        Business Type: ${businessType || 'General business'}

        Generate a detailed checklist with 5-15 items that covers all important aspects.
        Each item should be:
        - Specific and actionable
        - Clearly worded
        - Logically ordered
        - Include verification requirements where appropriate

        Consider including items for:
        - Preparation steps
        - Execution steps
        - Quality checks
        - Documentation requirements
        - Follow-up actions

        Format as a JSON array with each item having: title, description, isRequired, requiresVerification, order
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      return this.parseChecklistItems(text);
    } catch (error) {
      console.error('Error generating checklist template:', error);
      return [];
    }
  }

  // ==================== PRODUCTIVITY ANALYSIS ====================

  /**
   * Analyze staff productivity and provide insights
   */
  async analyzeStaffProductivity(
    staffId: string,
    period: { start: Date; end: Date }
  ): Promise<AIProductivityAnalysis> {
    try {
      const model = this.genAI.getGenerativeModel({
        model: "gemini-2.5-flash-preview-04-17",
        generationConfig: {
          temperature: 0.4,
          topP: 0.9,
          maxOutputTokens: 2000,
        }
      });

      // Get staff data and task history
      const staff = await this.staffService.getStaffById(staffId).toPromise();
      const tasks = await this.taskService.getStaffTasks(staffId, true).toPromise();

      // Filter tasks by period
      const periodTasks = tasks?.filter(task =>
        task.createdAt >= period.start && task.createdAt <= period.end
      ) || [];

      const completedTasks = periodTasks.filter(task => task.status === 'completed');
      const overdueTasks = periodTasks.filter(task =>
        task.status === 'overdue' || (task.dueDate && task.dueDate < new Date() && task.status !== 'completed')
      );

      const prompt = `
        Analyze the productivity of this staff member:

        Staff: ${staff?.firstName} ${staff?.lastName}
        Position: ${staff?.position}
        Analysis Period: ${period.start.toDateString()} to ${period.end.toDateString()}

        Task Statistics:
        - Total Tasks: ${periodTasks.length}
        - Completed Tasks: ${completedTasks.length}
        - Overdue Tasks: ${overdueTasks.length}
        - Completion Rate: ${periodTasks.length > 0 ? (completedTasks.length / periodTasks.length * 100).toFixed(1) : 0}%

        Task Details:
        ${periodTasks.map(task => `
          - ${task.title}: ${task.status} (Priority: ${task.priority}, Category: ${task.category})
            ${task.dueDate ? `Due: ${task.dueDate.toDateString()}` : 'No due date'}
            ${task.completedDate ? `Completed: ${task.completedDate.toDateString()}` : ''}
        `).join('\n')}

        Provide insights on:
        1. Productivity patterns and trends
        2. Strengths and areas for improvement
        3. Task completion efficiency
        4. Time management effectiveness
        5. Workload balance
        6. Specific recommendations for improvement

        Format as JSON with insights and recommendations arrays.
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      return this.parseProductivityAnalysis(text, staffId, staff?.firstName + ' ' + staff?.lastName || 'Unknown', period);
    } catch (error) {
      console.error('Error analyzing staff productivity:', error);
      return this.getDefaultProductivityAnalysis(staffId, period);
    }
  }

  /**
   * Analyze overall workload distribution and balance
   */
  async analyzeWorkloadBalance(businessId: string): Promise<AIWorkloadAnalysis> {
    try {
      const model = this.genAI.getGenerativeModel({
        model: "gemini-2.5-flash-preview-04-17",
        generationConfig: {
          temperature: 0.5,
          topP: 0.9,
          maxOutputTokens: 3000,
        }
      });

      // Get business data
      const staff = await this.staffService.getActiveStaff(businessId).toPromise();
      const allTasks = await this.taskService.getTasks({ businessId }, { field: 'createdAt', direction: 'desc' }).toPromise();

      const workloadData = staff?.map(member => {
        const memberTasks = allTasks?.filter(task => task.assignedTo.includes(member.id)) || [];
        const activeTasks = memberTasks.filter(task => !['completed', 'cancelled'].includes(task.status));

        return {
          staffId: member.id,
          name: `${member.firstName} ${member.lastName}`,
          position: member.position,
          totalTasks: memberTasks.length,
          activeTasks: activeTasks.length,
          completedTasks: memberTasks.filter(task => task.status === 'completed').length,
          overdueTasks: memberTasks.filter(task =>
            task.dueDate && task.dueDate < new Date() && task.status !== 'completed'
          ).length
        };
      }) || [];

      const prompt = `
        Analyze the workload distribution across this business:

        Business ID: ${businessId}
        Total Staff: ${staff?.length || 0}
        Total Tasks: ${allTasks?.length || 0}

        Staff Workload Breakdown:
        ${workloadData.map(member => `
          ${member.name} (${member.position}):
          - Total Tasks: ${member.totalTasks}
          - Active Tasks: ${member.activeTasks}
          - Completed: ${member.completedTasks}
          - Overdue: ${member.overdueTasks}
          - Completion Rate: ${member.totalTasks > 0 ? (member.completedTasks / member.totalTasks * 100).toFixed(1) : 0}%
        `).join('\n')}

        Analyze and provide:
        1. Workload balance assessment
        2. Identify overloaded or underutilized staff
        3. Task distribution patterns
        4. Efficiency opportunities
        5. Recommendations for better balance
        6. Suggested task redistributions

        Format as JSON with analysis and recommendations.
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      return this.parseWorkloadAnalysis(text, businessId);
    } catch (error) {
      console.error('Error analyzing workload balance:', error);
      return this.getDefaultWorkloadAnalysis(businessId);
    }
  }

  // ==================== TASK OPTIMIZATION ====================

  /**
   * Get AI suggestions for optimizing existing tasks
   */
  async optimizeTask(task: Task): Promise<AISuggestion[]> {
    try {
      const model = this.genAI.getGenerativeModel({
        model: "gemini-2.5-flash-preview-04-17",
        generationConfig: {
          temperature: 0.6,
          topP: 0.8,
          maxOutputTokens: 1500,
        }
      });

      const prompt = `
        Analyze this task and suggest optimizations:

        Task: ${task.title}
        Description: ${task.description || 'No description'}
        Status: ${task.status}
        Priority: ${task.priority}
        Category: ${task.category}
        Due Date: ${task.dueDate?.toDateString() || 'No due date'}
        Estimated Duration: ${task.estimatedDuration || 'Not specified'} minutes
        Progress: ${task.progress}%
        Assigned To: ${task.assignedTo.length} staff member(s)

        Provide optimization suggestions for:
        1. Task breakdown and structure
        2. Time estimation accuracy
        3. Priority and scheduling
        4. Resource allocation
        5. Dependencies and workflow
        6. Completion requirements

        Format as JSON array of suggestions with type, suggestion, reasoning, and confidence.
      `;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      return this.parseOptimizationSuggestions(text);
    } catch (error) {
      console.error('Error optimizing task:', error);
      return [];
    }
  }

  // ==================== HELPER METHODS ====================

  private parseTaskSuggestions(aiResponse: string): AITaskSuggestion[] {
    try {
      // Extract JSON from AI response
      const jsonMatch = aiResponse.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const suggestions = JSON.parse(jsonMatch[0]);
        return suggestions.map((s: any) => ({
          type: 'task-creation',
          title: s.title || 'AI Generated Task',
          description: s.description || '',
          reasoning: s.reasoning || 'AI generated suggestion',
          confidence: s.confidence || 0.7,
          priority: s.priority || 'medium',
          estimatedImpact: s.estimatedImpact || 'Positive impact expected'
        });
      }
    } catch (error) {
      console.error('Error parsing task suggestions:', error);
    }
    return [];
  }

  private parseChecklistItems(aiResponse: string): ChecklistItem[] {
    try {
      const jsonMatch = aiResponse.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const items = JSON.parse(jsonMatch[0]);
        return items.map((item: any, index: number) => ({
          id: `ai-generated-${Date.now()}-${index}`,
          title: item.title || 'Checklist Item',
          description: item.description,
          isCompleted: false,
          isRequired: item.isRequired !== false,
          order: item.order || index + 1,
          requiresVerification: item.requiresVerification || false,
          aiGenerated: true
        });
      }
    } catch (error) {
      console.error('Error parsing checklist items:', error);
    }
    return [];
  }

  private parseProductivityAnalysis(
    aiResponse: string,
    staffId: string,
    staffName: string,
    period: { start: Date; end: Date }
  ): AIProductivityAnalysis {
    // Implementation for parsing AI productivity analysis
    return this.getDefaultProductivityAnalysis(staffId, period);
  }

  private parseWorkloadAnalysis(aiResponse: string, businessId: string): AIWorkloadAnalysis {
    // Implementation for parsing AI workload analysis
    return this.getDefaultWorkloadAnalysis(businessId);
  }

  private parseOptimizationSuggestions(aiResponse: string): AISuggestion[] {
    try {
      const jsonMatch = aiResponse.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        const suggestions = JSON.parse(jsonMatch[0]);
        return suggestions.map((s: any) => ({
          id: `ai-opt-${Date.now()}-${Math.random()}`,
          type: s.type || 'task-optimization',
          suggestion: s.suggestion || 'Optimization suggestion',
          confidence: s.confidence || 0.7,
          reasoning: s.reasoning || 'AI analysis',
          createdAt: new Date()
        });
      }
    } catch (error) {
      console.error('Error parsing optimization suggestions:', error);
    }
    return [];
  }

  private getDefaultProductivityAnalysis(staffId: string, period: { start: Date; end: Date }): AIProductivityAnalysis {
    return {
      staffId,
      staffName: 'Unknown',
      period,
      metrics: {
        tasksCompleted: 0,
        averageCompletionTime: 0,
        overdueRate: 0,
        productivityScore: 0
      },
      insights: [],
      recommendations: []
    };
  }

  private getDefaultWorkloadAnalysis(businessId: string): AIWorkloadAnalysis {
    return {
      businessId,
      period: { start: new Date(), end: new Date() },
      overallMetrics: {
        totalTasks: 0,
        completionRate: 0,
        averageTaskDuration: 0,
        workloadDistribution: {}
      },
      staffAnalysis: [],
      recommendations: [],
      optimizationOpportunities: []
    };
  }
}
