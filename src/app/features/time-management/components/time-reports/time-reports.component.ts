import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'app-time-reports',
  standalone: true,
  imports: [
    CommonModule,
    CardModule,
    ButtonModule
  ],
  template: `
    <div class="time-reports-container">
      <div class="placeholder-content">
        <p-card class="placeholder-card">
          <ng-template pTemplate="header">
            <h3>
              <i class="pi pi-circle"></i>
              Time Reports & Analytics
            </ng-template>
        <ng-template pTemplate="content">
            <div class="placeholder-message">
              <i class="pi pi-circle"></i>
              <h3>Time Reports Coming Soon</h3>
              <p>This feature will include:</p>
              <ul>
                <li>Attendance reports and summaries</li>
                <li>Overtime and labor cost analysis</li>
                <li>Productivity metrics and trends</li>
                <li>Time off usage reports</li>
                <li>AI-powered insights and recommendations</li>
                <li>Exportable reports (PDF, Excel)</li>
              </ul>
            </div><ng-template pTemplate="footer">
            <p-button severity="primary" disabled>
              <i class="pi pi-circle"></i>
              Generate Report
            </p-button>
            <p-button disabled>
              <i class="pi pi-brain"></i>
              AI Insights
            </p-button>
        </ng-template>
      </p-card>
      </div>
  `,
  styles: [`
    .time-reports-container {
      padding: 24px;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .placeholder-card {
      max-width: 500px;
      width: 100%;
    }

    .placeholder-message {
      text-align: center;
      padding: 24px;

      mat-icon {
        font-size: 64px;
        width: 64px;
        height: 64px;
        color: #1976d2;
        margin-bottom: 16px;
      }

      h3 {
        margin: 0 0 16px 0;
        color: #333;
      }

      p {
        margin: 0 0 16px 0;
        color: #666;
      }

      ul {
        text-align: left;
        color: #555;
        
        li {
          margin-bottom: 8px;
        }
      }
    }

    mat-card-actions {
      display: flex;
      gap: 12px;
      justify-content: center;
    }
  `]
})
export class TimeReportsComponent {}
