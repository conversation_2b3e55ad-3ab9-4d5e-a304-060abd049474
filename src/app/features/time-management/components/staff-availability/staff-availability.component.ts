import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Observable, combineLatest } from 'rxjs';
import { map, startWith } from 'rxjs/operators';

// Angular Material
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

import { TableModule } from 'primeng/table';

import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { ChipModule } from 'primeng/chip';
import { TooltipModule } from 'primeng/tooltip';
import { ProgressSpinnerModule } from 'primeng/progressspinner';

// Services
import { AuthService } from '../../../../core/auth/auth.service';
import { StaffFirestoreService } from '../../../staff/services/staff-firestore.service';

// Models
import { StaffMember, StaffAvailability, DayAvailability } from '../../../staff/models/staff.model';

export interface StaffAvailabilityData {
  staffId: string;
  staffName: string;
  position: string;
  weeklyAvailability: StaffAvailability;
  timeOffRequests: any[];
  totalHoursAvailable: number;
}

@Component({
  selector: 'app-staff-availability',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CardModule,
    ButtonModule,
    TableModule, DropdownModule,
    CheckboxModule,
    ChipModule,
    TooltipModule,
    ProgressSpinnerModule
  ],
  template: `
    <div class="availability-container">
      <div class="availability-header">
        <h2>
          <i class="pi pi-users"></i>
          Staff Availability Overview
        </h2>
        <div class="header-actions">
          <p-button severity="primary" (click)="refreshAvailability()">
            <i class="pi pi-refresh"></i>
            Refresh
          </p-button>
          <p-button severity="secondary" (click)="generateAIInsights()">
            <i class="pi pi-brain"></i>
            AI Insights
          </p-button>
        </div>

      <div class="availability-filters">
        <div class="p-field" appearance="outline">
          <label>Filter by Position</label>
          <p-dropdown [formControl]="positionFilter" multiple [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
        </div>

        <div class="p-field" appearance="outline">
          <label>View Mode</label>
          <p-dropdown [formControl]="viewModeFilter" [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
        </div>

      <div class="availability-grid" *ngIf="filteredAvailability$ | async as availability; else loading">
        <p-card *ngFor="let staff of availability" class="staff-availability-card">
          <ng-template pTemplate="header">
            <div mat-card-avatar class="staff-avatar">
              <i class="pi pi-user"></i>
            </div>
            <h3>
            <p>Content</p><ng-template pTemplate="content">
            <div class="weekly-schedule">
              <div class="day-availability"
                   *ngFor="let day of daysOfWeek; let i = index"
                   [class.available]="getDayAvailability(staff.weeklyAvailability, day)?.available"
                   [class.unavailable]="!getDayAvailability(staff.weeklyAvailability, day)?.available">
                <div class="day-name">{{ dayLabels[i] }}</div>
                <div class="day-hours" *ngIf="getDayAvailability(staff.weeklyAvailability, day)?.available">
                  {{ getDayAvailability(staff.weeklyAvailability, day)?.startTime }} -
                  {{ getDayAvailability(staff.weeklyAvailability, day)?.endTime }}
                </div>
                <div class="day-unavailable" *ngIf="!getDayAvailability(staff.weeklyAvailability, day)?.available">
                  Unavailable
                </div>
            </div>

            <div class="time-off-summary" *ngIf="staff.timeOffRequests.length > 0">
              <h4>Upcoming Time Off:</h4>
              <div class="chip-container">
                <p-chip *ngFor="let request of staff.timeOffRequests.slice(0, 3)">
                  {{ request.startDate | date:'MMM d' }} - {{ request.endDate | date:'MMM d' }}
                </p-chip>
              </div><ng-template pTemplate="footer">
            <p-button severity="primary" (click)="editAvailability(staff.staffId)">
              <i class="pi pi-pencil"></i>
              Edit
            </p-button>
            <p-button (click)="viewDetails(staff.staffId)">
              <i class="pi pi-eye"></i>
              Details
            </p-button>
            <p-button severity="secondary" (click)="getAIRecommendations(staff.staffId)">
              <i class="pi pi-brain"></i>
              AI Analysis
            </p-button></ng-template></p-card>
      </div>

      <ng-template #loading>
        <div class="loading-container">
          <p-progressSpinner></p-progressSpinner>
          <p>Loading staff availability...</p>
        </div><!-- AI Insights Panel -->
      <p-card class="ai-insights-panel" *ngIf="aiInsights">
        <ng-template pTemplate="header">
          <h3>
            <i class="pi pi-brain"></i>
            AI Availability Insights
          <ng-template pTemplate="content">
          <div [innerHTML]="aiInsights"></div></ng-template>
        <ng-template pTemplate="footer">
          <p-button (click)="aiInsights = null">Close</p-button></ng-template></p-card>
    </div>
  `,
  styleUrls: ['./staff-availability.component.scss']
})
export class StaffAvailabilityComponent implements OnInit {
  private authService = inject(AuthService);
  private staffService = inject(StaffFirestoreService);
  private fb = inject(FormBuilder);

  // Form controls
  positionFilter = this.fb.control([]);
  viewModeFilter = this.fb.control('all');

  // Data
  staffMembers$!: Observable<StaffMember[]>;
  filteredAvailability$!: Observable<StaffAvailabilityData[]>;
  availablePositions: string[] = [];
  daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  dayLabels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

  // AI Insights
  aiInsights: string | null = null;

  ngOnInit(): void {
    this.loadStaffAvailability();
  }

  private loadStaffAvailability(): void {
    this.authService.userProfile$.subscribe(profile => {
      if (profile?.primaryBusinessId) {
        this.staffMembers$ = this.staffService.getActiveStaff(profile.primaryBusinessId);

        this.filteredAvailability$ = combineLatest([
          this.staffMembers$,
          this.positionFilter.valueChanges.pipe(startWith([]), map(val => val || [])),
          this.viewModeFilter.valueChanges.pipe(startWith('all'), map(val => val || 'all'))
        ]).pipe(
          map(([staff, positionFilters, viewMode]) =>
            this.transformToAvailabilityData(staff, positionFilters || [], viewMode)
          )
        );

        // Extract available positions
        this.staffMembers$.subscribe(staff => {
          this.availablePositions = [...new Set(staff.map(s => s.position))];
        });
      }
    });
  }

  private transformToAvailabilityData(
    staff: StaffMember[],
    positionFilters: string[],
    viewMode: string
  ): StaffAvailabilityData[] {
    let filteredStaff = staff;

    // Apply position filter
    if (positionFilters && positionFilters.length > 0) {
      filteredStaff = staff.filter(s => positionFilters.includes(s.position));
    }

    // Transform to availability data
    const availabilityData = filteredStaff.map(member => ({
      staffId: member.id || '',
      staffName: `${member.firstName} ${member.lastName}`,
      position: member.position,
      weeklyAvailability: member.availability || this.getDefaultAvailability(),
      timeOffRequests: [], // TODO: Load from time off service
      totalHoursAvailable: this.calculateTotalHours(member.availability || {})
    });

    // Apply view mode filter
    switch (viewMode) {
      case 'available':
        return availabilityData.filter(a => a.totalHoursAvailable > 0);
      case 'unavailable':
        return availabilityData.filter(a => a.totalHoursAvailable === 0);
      case 'limited':
        return availabilityData.filter(a => a.totalHoursAvailable > 0 && a.totalHoursAvailable < 40);
      default:
        return availabilityData;
    }
  }

  private getDefaultAvailability(): StaffAvailability {
    const defaultDay: DayAvailability = {
      available: false,
      startTime: '09:00',
      endTime: '17:00',
      notes: ''
    };

    return {
      monday: defaultDay,
      tuesday: defaultDay,
      wednesday: defaultDay,
      thursday: defaultDay,
      friday: defaultDay,
      saturday: defaultDay,
      sunday: defaultDay
    };
  }

  private calculateTotalHours(availability: StaffAvailability): number {
    let totalHours = 0;
    this.daysOfWeek.forEach(day => {
      const dayAvailability = this.getDayAvailability(availability, day);
      if (dayAvailability?.available && dayAvailability.startTime && dayAvailability.endTime) {
        const start = new Date(`2000-01-01 ${dayAvailability.startTime}`);
        const end = new Date(`2000-01-01 ${dayAvailability.endTime}`);
        totalHours += (end.getTime() - start.getTime()) / (1000 * 60 * 60);
      }
    });
    return totalHours;
  }

  refreshAvailability(): void {
    this.loadStaffAvailability();
  }

  async generateAIInsights(): Promise<void> {
    // TODO: Implement AI insights using Gemini 2.5 Flash
    this.aiInsights = `
      <h3>Availability Analysis</h3>
      <p><strong>Coverage Gaps:</strong> Low coverage on weekends and Monday mornings.</p>
      <p><strong>Recommendations:</strong></p>
      <ul>
        <li>Consider incentives for weekend shifts</li>
        <li>Cross-train staff for better flexibility</li>
        <li>Review Monday morning staffing needs</li>
      </ul>
    `;
  }

  editAvailability(staffId: string): void {
    // TODO: Open availability edit dialog
    console.log('Edit availability for:', staffId);
  }

  viewDetails(staffId: string): void {
    // TODO: Navigate to detailed availability view
    console.log('View details for:', staffId);
  }

  async getAIRecommendations(staffId: string): Promise<void> {
    // TODO: Get AI recommendations for specific staff member
    console.log('Get AI recommendations for:', staffId);
  }

  getDayAvailability(availability: StaffAvailability, day: string): DayAvailability | null {
    if (!availability) return null;

    switch (day) {
      case 'monday': return availability.monday;
      case 'tuesday': return availability.tuesday;
      case 'wednesday': return availability.wednesday;
      case 'thursday': return availability.thursday;
      case 'friday': return availability.friday;
      case 'saturday': return availability.saturday;
      case 'sunday': return availability.sunday;
      default: return null;
    }
  }
}
