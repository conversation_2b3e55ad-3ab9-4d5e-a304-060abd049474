import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CdkDragDrop, DragDropModule, moveItemInArray } from '@angular/cdk/drag-drop';
import { MatGridListModule } from '@angular/material/grid-list';
import { ButtonModule } from 'primeng/button';
import { MessageService } from 'primeng/api';

import { CardModule } from 'primeng/card';
import { MenuModule } from 'primeng/menu';
import { TooltipModule } from 'primeng/tooltip';
import { ToastModule } from 'primeng/toast';
import { DividerModule } from 'primeng/divider';
import { DialogService } from 'primeng/dynamicdialog';
import { Subscription, Observable } from 'rxjs';

import { NumberWidgetComponent } from '../../dashboard/widgets/number-widget.component';
import { UpcomingShiftsWidgetComponent } from '../../dashboard/widgets/upcoming-shifts-widget.component';
import { DashboardStateService, DashboardWidget } from '../../dashboard/services/dashboard-state.service';
import { WidgetDataService } from '../../dashboard/services/widget-data.service';
import { StaffManagerThemeService } from '../../core/theme/staffmanager-theme';
import { BusinessService, BusinessData } from '../../services/business.service';

export interface EnhancedWidget extends DashboardWidget {
  type: 'number' | 'shifts' | 'chart' | 'list' | 'custom';
  size: 'small' | 'medium' | 'large';
  position: { x: number; y: number };
  data?: any;
  config?: any;
}

@Component({
  selector: 'app-enhanced-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    DragDropModule,
    MatGridListModule,
    ButtonModule,
    CardModule,
    MenuModule,
    TooltipModule,
    ToastModule,
    DividerModule,
    NumberWidgetComponent,
    UpcomingShiftsWidgetComponent
  ],
  template: `
    <div class="enhanced-dashboard" [class.dark-theme]="themeService.isDark()">
      <!-- Dashboard Header -->
      <div class="dashboard-header">
        <div class="header-content">
          <div class="header-left">
            <h1 class="dashboard-title">
              <i class="pi pi-th-large"></i>
              StaffManager Dashboard
            </h1>
            <p class="dashboard-subtitle">Welcome back! Here's what's happening today.</p>
          </div>

          <div class="header-actions">
            <p-button [text]="true"
                    [pTooltip]="editMode ? 'Exit Edit Mode' : 'Enter Edit Mode'"
                    (click)="toggleEditMode()"
                    [class.active]="editMode"
                    class="action-btn">
              <i class="pi pi-pencil"></i>
            </p-button>

            <p-button [text]="true"
                    pTooltip="Add Widget"
                    [disabled]="!editMode"
                    (click)="showAddWidgetMenu($event)"
                    class="action-btn">
              <i class="pi pi-plus"></i>
            </p-button>

            <p-button [text]="true"
                    pTooltip="Refresh Data"
                    (click)="refreshData()"
                    class="action-btn">
              <i class="pi pi-refresh"></i>
            </p-button>

            <p-button [text]="true"
                    pTooltip="Toggle Theme"
                    (click)="toggleTheme()"
                    class="action-btn">
              <i class="pi pi-palette"></i>
            </p-button>
          </div>

      <!-- Edit Mode Banner -->
      <div class="edit-mode-banner" *ngIf="editMode">
        <i class="pi pi-info-circle"></i>
        <span>Edit Mode: Drag widgets to rearrange, click settings to configure</span>
        <p-button severity="primary" (click)="saveLayout()">Save Layout</p-button>
      </div>

      <!-- Dashboard Cards Grid -->
      <div class="dashboard-cards-grid">
        <!-- Staff On Shift Card -->
        <p-card class="dashboard-card staff-card">
          <ng-template pTemplate="header">
            <div class="card-header">
              <div class="card-header-left">
                <i class="pi pi-users"></i>
                <h3 class="card-title">Staff On Shift</h3>
              </div>
              <div class="card-actions">
                <p-button icon="pi pi-refresh" [text]="true"></p-button>
                <p-button icon="pi pi-ellipsis-v" [text]="true"></p-button>
              </div>
          </ng-template>

          <ng-template pTemplate="content">
            <div class="card-number">{{ currentBusinessData?.staffOnShift || 5 }}</div>
            <div class="card-subtitle">Staff members currently on shift</div>

            <div class="card-content">
              <div class="section-title">Currently Working:</div>
              <ul class="staff-list">
                <li class="staff-item" *ngFor="let staff of currentBusinessData?.staffList || []">
                  <span class="staff-name">{{ staff.name }}</span>
                  <span class="staff-role">{{ staff.role }}</span>
                </li>
              </ul>
            </div>
          </ng-template>
        </p-card>

        <!-- Upcoming Shifts Card -->
        <p-card class="dashboard-card shifts-card">
          <ng-template pTemplate="header">
            <div class="card-header">
              <div class="card-header-left">
                <i class="pi pi-calendar"></i>
                <h3 class="card-title">Upcoming Shifts</h3>
              </div>
              <div class="card-actions">
                <p-button icon="pi pi-refresh" [text]="true"></p-button>
                <p-button icon="pi pi-ellipsis-v" [text]="true"></p-button>
              </div>
          </ng-template>

          <ng-template pTemplate="content">
            <div class="card-number">{{ currentBusinessData?.upcomingShifts || 8 }}</div>
            <div class="card-subtitle">Upcoming shifts today</div>

            <div class="card-content">
              <div class="section-title">Today's Schedule:</div>
              <ul class="schedule-list">
                <li class="schedule-item" *ngFor="let schedule of currentBusinessData?.scheduleList || []">
                  <span class="schedule-time">{{ schedule.time }}</span>
                  <span class="schedule-staff">{{ schedule.staff }}</span>
                </li>
              </ul>
            </div>
          </ng-template>
        </p-card>

        <!-- Notifications Card -->
        <p-card class="dashboard-card notifications-card">
          <ng-template pTemplate="header">
            <div class="card-header">
              <div class="card-header-left">
                <i class="pi pi-bell"></i>
                <h3 class="card-title">Notifications</h3>
              </div>
              <div class="card-actions">
                <p-button icon="pi pi-refresh" [text]="true"></p-button>
                <p-button icon="pi pi-ellipsis-v" [text]="true"></p-button>
              </div>
          </ng-template>

          <ng-template pTemplate="content">
            <div class="card-number">{{ currentBusinessData?.notifications || 3 }}</div>
            <div class="card-subtitle">New notifications</div>

            <div class="card-content">
              <div class="notification-item" *ngFor="let notification of currentBusinessData?.notificationList || []">
                <div class="notification-title">{{ notification.title }}</div>
                <div class="notification-time">{{ notification.time }}</div>
            </div>
          </ng-template>
        </p-card>

        <!-- Messages Card -->
        <p-card class="dashboard-card messages-card">
          <ng-template pTemplate="header">
            <div class="card-header">
              <div class="card-header-left">
                <i class="pi pi-envelope"></i>
                <h3 class="card-title">Messages</h3>
              </div>
              <div class="card-actions">
                <p-button icon="pi pi-refresh" [text]="true"></p-button>
                <p-button icon="pi pi-ellipsis-v" [text]="true"></p-button>
              </div>
          </ng-template>

          <ng-template pTemplate="content">
            <div class="card-number">{{ currentBusinessData?.messages || 7 }}</div>
            <div class="card-subtitle">Unread messages</div>

            <div class="card-content">
              <div class="message-item" *ngFor="let message of currentBusinessData?.messageList || []">
                <div class="message-title">{{ message.title }}</div>
                <div class="message-meta">{{ message.meta }}</div>
            </div>
          </ng-template>
        </p-card>
      </div>

      <!-- Edit Mode Widgets Container (when in edit mode) -->
      <div class="widgets-container"
           *ngIf="editMode"
           cdkDropList
           (cdkDropListDropped)="onWidgetDrop($event)">

        <div class="widget-wrapper"
             *ngFor="let widget of enhancedWidgets; trackBy: trackWidget"
             cdkDrag
             [cdkDragDisabled]="!editMode"
             [class.edit-mode]="editMode"
             [ngClass]="'size-' + widget.size">

          <!-- Widget Header (Edit Mode) -->
          <div class="widget-header" *ngIf="editMode">
            <div class="drag-handle" cdkDragHandle>
              <i class="pi pi-circle"></i>
            </div>
            <div class="widget-size-controls">
              <p-button text="true"
                      pTooltip="Small Size"
                      [class.active]="widget.size === 'small'"
                      (click)="resizeWidget(widget, 'small')">
                <i class="pi pi-circle"></i>
              </p-button>
              <p-button text="true"
                      pTooltip="Medium Size"
                      [class.active]="widget.size === 'medium'"
                      (click)="resizeWidget(widget, 'medium')">
                <i class="pi pi-circle"></i>
              </p-button>
              <p-button text="true"
                      pTooltip="Large Size"
                      [class.active]="widget.size === 'large'"
                      (click)="resizeWidget(widget, 'large')">
                <i class="pi pi-circle"></i>
              </p-button>
            </div>
            <div class="widget-actions">
              <p-button text="true"
                      pTooltip="Widget Settings"
                      (click)="openWidgetSettings(widget)">
                <i class="pi pi-cog"></i>
              </p-button>
              <p-button text="true"
                      pTooltip="Remove Widget"
                      (click)="removeWidget(widget)">
                <i class="pi pi-times"></i>
              </p-button>
            </div>

          <!-- Widget Content -->
          <div class="widget-content">
            <ng-container [ngSwitch]="widget.id">
              <app-number-widget
                *ngSwitchCase="'staff-on-shift'"
                [title]="'Staff On Shift'"
                [value]="(staffOnShift$ | async) ?? 0"
                [description]="'Staff members currently on shift'"
                [quickLook]="widget.size === 'small'">
              </app-number-widget>

              <app-upcoming-shifts-widget
                *ngSwitchCase="'upcoming-shifts'"
                [title]="'Upcoming Shifts'"
                [value]="((upcomingShifts$ | async)?.length ?? 0)"
                [description]="'Upcoming shifts today'"
                [shifts]="(upcomingShifts$ | async) ?? []">
              </app-upcoming-shifts-widget>

              <app-number-widget
                *ngSwitchCase="'notifications'"
                [title]="'Notifications'"
                [value]="(notifications$ | async) ?? 0"
                [description]="'Unread notifications'"
                [quickLook]="widget.size === 'small'">
              </app-number-widget>

              <app-number-widget
                *ngSwitchCase="'messages'"
                [title]="'Messages'"
                [value]="(messages$ | async) ?? 0"
                [description]="'Unread messages'"
                [quickLook]="widget.size === 'small'">
              </app-number-widget>

              <!-- Placeholder for unknown widgets -->
              <div *ngSwitchDefault class="widget-placeholder">
                <i class="pi pi-circle"></i>
                <h3>{{ widget.title }}</h3>
                <p>Widget type: {{ widget.type }}</p>
              </div>
            </ng-container>
          </div>

      <!-- Add Widget Menu -->
      <p-menu #addWidgetMenu [popup]="true" [model]="addWidgetMenuItems"></p-menu>
    </div>
  `,
  styleUrls: ['./enhanced-dashboard.component.scss']
})
export class EnhancedDashboardComponent implements OnInit, OnDestroy {
  enhancedWidgets: EnhancedWidget[] = [];
  editMode = false;
  private subscription = new Subscription();

  // Business data
  businessData$: Observable<BusinessData>;
  currentBusinessData: BusinessData | null = null;

  // Data observables
  staffOnShift$: Observable<number>;
  upcomingShifts$: Observable<any[]>;
  notifications$: Observable<number>;
  messages$: Observable<number>;

  // Menu items for add widget menu
  get addWidgetMenuItems() {
    return [
      {
        label: 'Staff Count Widget',
        icon: 'pi pi-users',
        command: () => this.addWidget('staff-count')
      },
      {
        label: 'Schedule Widget',
        icon: 'pi pi-clock',
        command: () => this.addWidget('schedule')
      },
      {
        label: 'Tasks Widget',
        icon: 'pi pi-check-square',
        command: () => this.addWidget('tasks')
      },
      {
        label: 'Notifications Widget',
        icon: 'pi pi-bell',
        command: () => this.addWidget('notifications')
      },
      {
        separator: true
      },
      {
        label: 'Custom Widget Builder',
        icon: 'pi pi-wrench',
        command: () => this.openCustomWidgetBuilder()
      }
    ];
  }

  constructor(
    private dashboardState: DashboardStateService,
    private widgetData: WidgetDataService,
    public themeService: StaffManagerThemeService,
    private dialog: DialogService,
    private messageService: MessageService,
    private businessService: BusinessService
  ) {
    // Initialize business data stream
    this.businessData$ = this.businessService.businessData$;

    // Initialize data streams (keeping existing for compatibility)
    this.staffOnShift$ = this.widgetData.getStaffOnShift();
    this.upcomingShifts$ = this.widgetData.getUpcomingShifts();
    this.notifications$ = this.widgetData.getNotifications();
    this.messages$ = this.widgetData.getMessages();
  }

  ngOnInit(): void {
    this.loadEnhancedWidgets();
    this.subscription.add(
      this.dashboardState.editMode$.subscribe(mode => this.editMode = mode)
    );

    // Subscribe to business data changes
    this.subscription.add(
      this.businessData$.subscribe(data => {
        this.currentBusinessData = data;
      })
    );
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  private loadEnhancedWidgets(): void {
    // Convert basic widgets to enhanced widgets with positioning and sizing
    const basicWidgets = [
      { id: 'staff-on-shift', title: 'Staff On Shift' },
      { id: 'upcoming-shifts', title: 'Upcoming Shifts' },
      { id: 'notifications', title: 'Notifications' },
      { id: 'messages', title: 'Messages' }
    ];

    this.enhancedWidgets = basicWidgets.map((widget, index) => ({
      ...widget,
      type: widget.id === 'upcoming-shifts' ? 'shifts' : 'number',
      size: widget.id === 'upcoming-shifts' ? 'large' : 'medium',
      position: { x: (index % 2) * 300, y: Math.floor(index / 2) * 200 }
    })) as EnhancedWidget[];
  }

  toggleEditMode(): void {
    this.editMode = !this.editMode;
    this.dashboardState.setEditMode(this.editMode);

    const message = this.editMode ? 'Edit mode enabled' : 'Edit mode disabled';
    this.messageService.add({ severity: 'info', summary: message });
  }

  toggleTheme(): void {
    this.themeService.toggleTheme();
  }

  refreshData(): void {
    // Trigger data refresh
    this.messageService.add({ severity: 'info', summary: 'Data refreshed' });
  }

  showAddWidgetMenu(event: Event): void {
    // This method will be called when the add widget button is clicked
    // The menu will be shown automatically via the [model] binding
  }

  onWidgetDrop(event: CdkDragDrop<EnhancedWidget[]>): void {
    if (event.previousIndex !== event.currentIndex) {
      moveItemInArray(this.enhancedWidgets, event.previousIndex, event.currentIndex);
      this.saveLayout();
    }
  }

  addWidget(type: string): void {
    const newWidget: EnhancedWidget = {
      id: `${type}-${Date.now()}`,
      title: `New ${type} Widget`,
      type: 'number',
      size: 'medium',
      position: { x: 0, y: 0 }
    };

    this.enhancedWidgets.push(newWidget);
    this.messageService.add({ severity: 'info', summary: 'Widget added' });
  }

  removeWidget(widget: EnhancedWidget): void {
    const index = this.enhancedWidgets.findIndex(w => w.id === widget.id);
    if (index > -1) {
      this.enhancedWidgets.splice(index, 1);
      this.messageService.add({ severity: 'info', summary: 'Widget removed' });
    }
  }

  resizeWidget(widget: EnhancedWidget, size: 'small' | 'medium' | 'large'): void {
    widget.size = size;
    this.saveLayout();
    this.messageService.add({ severity: 'info', summary: `Widget resized to ${size}` });
  }

  openWidgetSettings(widget: EnhancedWidget): void {
    // Import and open widget settings dialog
    import('../../dashboard/widget-settings-dialog.component').then(({ WidgetSettingsDialogComponent }) => {
      const dialogRef = this.dialog.open(WidgetSettingsDialogComponent, {
        data: { settings: widget.config || {} },
        width: '400px'
      });

      dialogRef.onClose.subscribe(result => {
        if (result) {
          widget.config = result;
          this.saveLayout();
        }
      });
    });
  }

  openCustomWidgetBuilder(): void {
    // Import and open custom widget builder dialog
    import('../../dashboard/custom-widget-builder/custom-widget-builder.component').then(({ CustomWidgetBuilderComponent }) => {
      const dialogRef = this.dialog.open(CustomWidgetBuilderComponent, {
        width: '90vw',
        height: '90vh'
      });

      dialogRef.onClose.subscribe(result => {
        if (result) {
          const newWidget: EnhancedWidget = {
            id: result.id,
            title: result.title,
            type: result.type,
            size: result.size,
            position: { x: 0, y: 0 },
            config: result
          };
          this.enhancedWidgets.push(newWidget);
          this.saveLayout();
          this.messageService.add({ severity: 'info', summary: 'Custom widget created successfully!' });
        }
      });
    });
  }

  saveLayout(): void {
    // Save widget layout to localStorage
    localStorage.setItem('enhanced-dashboard-layout', JSON.stringify(this.enhancedWidgets));
    this.messageService.add({ severity: 'info', summary: 'Layout saved' });
  }

  trackWidget(index: number, widget: EnhancedWidget): string {
    return widget.id;
  }
}
