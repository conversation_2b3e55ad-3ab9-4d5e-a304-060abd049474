import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormArray } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Observable, map } from 'rxjs';

// Angular Material
import { CardModule } from 'primeng/card';
import { ToastModule } from 'primeng/toast';

import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';

import { ButtonModule } from 'primeng/button';

import { CheckboxModule } from 'primeng/checkbox';
import { MatSliderModule } from '@angular/material/slider';
import { ChipModule } from 'primeng/chip';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { AccordionModule } from 'primeng/accordion';
import { DividerModule } from 'primeng/divider';
import { MessageService } from 'primeng/api';
import { ToolbarModule } from 'primeng/toolbar';

// Services
import { StaffFirestoreService } from '../../staff/services/staff-firestore.service';
import { AuthService } from '../../../core/auth/auth.service';

// Models
import { StaffGoalExtended, StaffMember, GoalMilestone } from '../../staff/models/staff.model';

@Component({
  selector: 'app-goal-dialog',
  standalone: true,
  imports: [
    ToastModule,
    CommonModule,
    ReactiveFormsModule,
    CardModule,
    InputTextModule, DropdownModule,
    CalendarModule,
    ButtonModule,
    CheckboxModule,
    MatSliderModule,
    ChipModule,
    AutoCompleteModule,
    AccordionModule,
    DividerModule,
    ToolbarModule
  ],
  template: `
    <div class="goal-dialog-container">
      <p-toolbar severity="primary">
        <p-button text="true" (click)="goBack()">
          <i class="pi pi-circle"></i>
        </p-button>
        <span>{{ isEditMode ? 'Edit Goal' : 'Create New Goal' }}</span>
        <span class="spacer"></span>
        <p-button text="true" (click)="getAISuggestions()" pTooltip="Get AI Suggestions">
          <i class="pi pi-brain"></i>
        </p-button>
      </p-toolbar>

      <div class="goal-form-content">
        <form [formGroup]="goalForm" (ngSubmit)="onSubmit()" class="goal-form">
          <!-- Basic Information -->
          <p-card class="form-section">
            <ng-template pTemplate="header"><h3>Header</h3><ng-template pTemplate="content">
              <div class="form-row">
                <div class="p-field" appearance="outline" class="full-width">
                  <label>Goal Title</label>
                  <input pInputText formControlName="title" placeholder="Enter goal title">
                  <mat-error *ngIf="goalForm.get('title')?.hasError('required')">
                    Title is required
                  </mat-error>
                  <mat-error *ngIf="goalForm.get('title')?.hasError('minlength')">
                    Title must be at least 3 characters
                  </mat-error>
                </div>

              <div class="form-row">
                <div class="p-field" appearance="outline" class="full-width">
                  <label>Description</label>
                  <textarea pInputText formControlName="description" rows="3"
                           placeholder="Describe the goal and what success looks like"></textarea>
                </div>

              <div class="form-row">
                <div class="p-field" appearance="outline">
                  <label>Goal Type</label>
                  <p-dropdown formControlName="type" [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
                </div>

                <div class="p-field" appearance="outline">
                  <label>Category</label>
                  <p-dropdown formControlName="category" [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
                </div>

                <div class="p-field" appearance="outline">
                  <label>Priority</label>
                  <p-dropdown formControlName="priority" [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
                </div></ng-template></p-card>

          <!-- Assignment and Timeline -->
          <p-card class="form-section">
            <ng-template pTemplate="header"><h3>Header</h3><ng-template pTemplate="content">
              <div class="form-row">
                <div class="p-field" appearance="outline" class="full-width">
                  <label>Assigned To</label>
                  <p-dropdown formControlName="assignedTo" multiple [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
                  <mat-hint>Select one or more staff members</mat-hint>
                </div>

              <div class="form-row">
                <div class="p-field" appearance="outline">
                  <label>Target Date</label>
                  <input pInputText  formControlName="targetDate">
                  
                  
                </div>

                <div class="p-field" appearance="outline">
                  <label>Current Progress (%)</label>
                  <input pInputText type="number" formControlName="progress" min="0" max="100">
                </div>

                <div class="p-field" appearance="outline">
                  <label>Status</label>
                  <p-dropdown formControlName="status" [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
                </div></ng-template></p-card>

          <!-- Target Values (Optional) -->
          <p-card class="form-section">
            <ng-template pTemplate="header"><h3>
              <p>Content</p><ng-template pTemplate="content">
              <div class="form-row">
                <div class="p-field" appearance="outline">
                  <label>Target Value</label>
                  <input pInputText type="number" formControlName="targetValue"
                         placeholder="e.g., 100">
                </div>

                <div class="p-field" appearance="outline">
                  <label>Current Value</label>
                  <input pInputText type="number" formControlName="currentValue"
                         placeholder="e.g., 25">
                </div>

                <div class="p-field" appearance="outline">
                  <label>Unit</label>
                  <p-dropdown formControlName="unit" [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
                </div></ng-template></p-card>

          <!-- Milestones -->
          <p-card class="form-section">
            <ng-template pTemplate="header"><h3>
              <p>Content</p><ng-template pTemplate="content">
              <div formArrayName="milestones">
                <div *ngFor="let milestone of milestones.controls; let i = index"
                     [formGroupName]="i" class="milestone-item">
                  <div class="p-field" appearance="outline" class="milestone-title">
                    <label>Milestone {{ i + 1 }}</label>
                    <input pInputText formControlName="title" placeholder="Milestone title">
                  </div>

                  <div class="p-field" appearance="outline" class="milestone-date">
                    <label>Target Date</label>
                    <input pInputText  formControlName="targetDate">
                    
                    
                  </div>

                  <p-checkbox formControlName="completed" class="milestone-completed">
                    Completed
                  </p-checkbox>

                  <p-button text="true" type="button" (click)="removeMilestone(i)"
                          color="warn" class="remove-milestone">
                    <i class="pi pi-trash"></i>
                  </p-button>
                </div>

              <p-button outlined="true" type="button" (click)="addMilestone()" class="add-milestone">
                <i class="pi pi-plus"></i>
                Add Milestone
              </p-button></ng-template></p-card>

          <!-- Advanced Options -->
          <p-accordionTab class="advanced-options">
            <p-accordionTab-header>
              <mat-panel-title>Advanced Options</mat-panel-title>
            </mat-expansion-panel-header>

            <div class="form-row">
              <div class="p-field" appearance="outline" class="full-width">
                <label>Tags</label>
                <input pInputText formControlName="tagsInput"
                       placeholder="Enter tags separated by commas">
                <mat-hint>e.g., sales, q1, priority</mat-hint>
              </div>

            <div class="form-row">
              <p-checkbox formControlName="isRecurring">
                Recurring Goal
              </p-checkbox>
            </div>

            <div class="form-row" *ngIf="goalForm.get('isRecurring')?.value">
              <div class="p-field" appearance="outline">
                <label>Recurrence Pattern</label>
                <p-dropdown formControlName="recurringPattern" [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
              </div>
          </p-accordionTab>

          <!-- Form Actions -->
          <div class="form-actions">
            <p-button type="button" (click)="goBack()">
              Cancel
            </p-button>
            <p-button severity="primary" type="submit"
                    [disabled]="goalForm.invalid || isSubmitting">
              <i class="pi pi-circle"></i>
              <i class="pi pi-circle"></i>
              {{ isSubmitting ? 'Saving...' : (isEditMode ? 'Update Goal' : 'Create Goal') }}
            </p-button>
          </div>
        </form>
      </div>
  `,
  styles: [`
    .goal-dialog-container {
      min-height: 100vh;
      background-color: #f5f5f5;
    }

    .spacer {
      flex: 1;
    }

    .goal-form-content {
      padding: 24px;
      max-width: 1000px;
      margin: 0 auto;
    }

    .goal-form {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    .form-section {
      margin-bottom: 24px;
    }

    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
      flex-wrap: wrap;
    }

    .form-row > * {
      flex: 1;
      min-width: 200px;
    }

    .full-width {
      width: 100%;
    }

    .milestone-item {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 16px;
      padding: 16px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      background-color: #fafafa;
    }

    .milestone-title {
      flex: 2;
    }

    .milestone-date {
      flex: 1;
    }

    .milestone-completed {
      flex: 0 0 auto;
    }

    .remove-milestone {
      flex: 0 0 auto;
    }

    .add-milestone {
      margin-top: 16px;
    }

    .advanced-options {
      margin-bottom: 24px;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 16px;
      padding: 24px;
      background-color: white;
      border-top: 1px solid #e0e0e0;
      position: sticky;
      bottom: 0;
      z-index: 10;
    }

    @media (max-width: 768px) {
      .goal-form-content {
        padding: 16px;
      }

      .form-row {
        flex-direction: column;
      }

      .form-row > * {
        min-width: unset;
      }

      .milestone-item {
        flex-direction: column;
        align-items: stretch;
      }

      .milestone-completed {
        align-self: flex-start;
      }
    }
  `]
})
export class GoalDialogComponent implements OnInit {
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private staffService = inject(StaffFirestoreService);
  private authService = inject(AuthService);
  private messageService = inject(MessageService);

  goalForm!: FormGroup;
  isEditMode = false;
  isSubmitting = false;
  goalId?: string;

  staffMembers$!: Observable<StaffMember[]>;

  ngOnInit(): void {
    this.initializeForm();
    this.loadStaffMembers();
    this.checkEditMode();
  }

  private initializeForm(): void {
    this.goalForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(3)]],
      description: [''],
      type: ['individual', Validators.required],
      category: ['performance', Validators.required],
      priority: ['medium', Validators.required],
      assignedTo: [[], Validators.required],
      targetDate: [null, Validators.required],
      progress: [0, [Validators.min(0), Validators.max(100)]],
      status: ['not-started', Validators.required],
      targetValue: [null],
      currentValue: [null],
      unit: [''],
      milestones: this.fb.array([]),
      tagsInput: [''],
      isRecurring: [false],
      recurringPattern: ['monthly']
    });
  }

  private loadStaffMembers(): void {
    this.staffMembers$ = this.staffService.subscribeToStaff();
  }

  private checkEditMode(): void {
    this.goalId = this.route.snapshot.paramMap.get('id') || undefined;
    this.isEditMode = !!this.goalId;

    if (this.isEditMode && this.goalId) {
      this.loadGoalForEdit(this.goalId);
    }
  }

  private loadGoalForEdit(goalId: string): void {
    // Load goal data and populate form
    // This would need to be implemented with the actual service method
    console.log('Loading goal for edit:', goalId);
  }

  get milestones(): FormArray {
    return this.goalForm.get('milestones') as FormArray;
  }

  addMilestone(): void {
    const milestoneGroup = this.fb.group({
      title: ['', Validators.required],
      targetDate: [null, Validators.required],
      completed: [false]
    });
    this.milestones.push(milestoneGroup);
  }

  removeMilestone(index: number): void {
    this.milestones.removeAt(index);
  }

  onSubmit(): void {
    if (this.goalForm.valid) {
      this.isSubmitting = true;
      const formValue = this.goalForm.value;

      // Process tags
      const tags = formValue.tagsInput ?
        formValue.tagsInput.split(',').map((tag: string) => tag.trim()).filter((tag: string) => tag) :
        [];

      const goalData: Omit<StaffGoalExtended, 'id' | 'createdAt' | 'updatedAt'> = {
        title: formValue.title,
        description: formValue.description,
        type: formValue.type,
        category: formValue.category,
        priority: formValue.priority,
        assignedTo: formValue.assignedTo,
        assignedBy: '', // Will be set from current user
        createdBy: '', // Will be set from current user
        targetDate: formValue.targetDate,
        progress: formValue.progress,
        status: formValue.status,
        targetValue: formValue.targetValue,
        currentValue: formValue.currentValue,
        unit: formValue.unit,
        tags,
        milestones: formValue.milestones,
        isRecurring: formValue.isRecurring,
        recurringPattern: formValue.recurringPattern,
        comments: []
      };

      // Set current user info
      this.authService.user$.subscribe(user => {
        if (user) {
          goalData.assignedBy = user.uid;
          goalData.createdBy = user.uid;

          if (this.isEditMode) {
            this.staffService.updateGoal(this.goalId!, goalData).subscribe({
              next: () => {
                this.messageService.add({ severity: 'info', summary: 'Goal updated successfully!' });
                this.router.navigate(['/goals']);
              },
              error: (error: any) => {
                console.error('Error updating goal:', error);
                this.messageService.add({ severity: 'info', summary: 'Error updating goal. Please try again.' });
                this.isSubmitting = false;
              }
            });
          } else {
            this.staffService.createGoal(goalData).subscribe({
              next: () => {
                this.messageService.add({ severity: 'info', summary: 'Goal created successfully!' });
                this.router.navigate(['/goals']);
              },
              error: (error: any) => {
                console.error('Error creating goal:', error);
                this.messageService.add({ severity: 'info', summary: 'Error creating goal. Please try again.' });
                this.isSubmitting = false;
              }
            });
          }
        }
      });
    }
  }

  getAISuggestions(): void {
    // Implement AI suggestions for goal creation
    this.messageService.add({ severity: 'info', summary: 'AI suggestions coming soon!' });
  }

  goBack(): void {
    this.router.navigate(['/goals']);
  }
}
