import { Component, OnInit, inject, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, combineLatest, of } from 'rxjs';
import { map, switchMap, shareReplay } from 'rxjs/operators';

// Angular Material
import { TabViewModule } from 'primeng/tabview';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

import { ChipModule } from 'primeng/chip';
import { ProgressBarModule } from 'primeng/progressbar';
import { BadgeModule } from 'primeng/badge';
import { MenuModule } from 'primeng/menu';
import { DividerModule } from 'primeng/divider';
import { ListboxModule } from 'primeng/listbox';

// Services
import { StaffFirestoreService } from '../services/staff-firestore.service';
import { CalendarService } from '../../calendar/services/calendar.service';
import { AuthService } from '../../../core/auth/auth.service';

// Models
import {
  StaffMember,
  StaffGoalExtended,
  StaffTask,
  TimeEntry,
  TimeOffRequest,
  WorkSchedule
} from '../models/staff.model';
import { CalendarEvent } from '../../calendar/models/calendar.model';

@Component({
  selector: 'app-staff-profile',
  standalone: true,
  imports: [
    CommonModule,
    TabViewModule,
    CardModule,
    ButtonModule,
    ChipModule,
    ProgressBarModule,
    BadgeModule,
    MenuModule,
    DividerModule,
    ListboxModule
  ],
  template: `
    <div class="staff-profile-container" *ngIf="staffMember$ | async as staff">
      <!-- Profile Header -->
      <p-card class="profile-header">
        <ng-template pTemplate="content">
          <div class="profile-header-content">
            <div class="avatar-section">
              <div class="avatar">
                <i class="pi pi-circle"></i>
                <img *ngIf="staff.avatar" [src]="staff.avatar" [alt]="staff.firstName + ' ' + staff.lastName">
              </div>
              <p-button text="true" [matMenuTriggerFor]="avatarMenu" *ngIf="canEdit">
                <i class="pi pi-pencil"></i>
              </p-button>
            </div>

            <div class="profile-info">
              <h1>{{ staff.firstName }} {{ staff.lastName }}</h1>
              <h2>{{ staff.position }}</h2>
              <p class="department">{{ staff.department }}</p>

              <div class="status-chips">
                <div class="chip-container">
                  <p-chip [class]="'status-' + staff.status">{{ staff.status | titlecase }}</p-chip>
                  <p-chip>{{ staff.employmentType | titlecase }}</p-chip>
                  <p-chip>{{ staff.accessLevel | titlecase }}</p-chip>
                </div>

              <div class="contact-info">
                <div class="contact-item">
                  <i class="pi pi-circle"></i>
                  <span>{{ staff.email }}</span>
                </div>
                <div class="contact-item">
                  <i class="pi pi-circle"></i>
                  <span>{{ staff.phone }}</span>
                </div>
                <div class="contact-item">
                  <i class="pi pi-circle"></i>
                  <span>Employee ID: {{ staff.employeeId }}</span>
                </div>
            </div>

            <div class="profile-actions" *ngIf="canEdit">
              <p-button severity="primary" (click)="editProfile()">
                <i class="pi pi-pencil"></i>
                Edit Profile
              </p-button>
              <p-button [matMenuTriggerFor]="actionsMenu">
                <i class="pi pi-ellipsis-v"></i>
              </p-button>
            </div></ng-template></p-card>

      <!-- Profile Tabs -->
      <p-tabView class="profile-tabs" animationDuration="300ms">
        <!-- Overview Tab -->
        <p-tabPanel label="Overview">
          <div class="tab-content">
            <div class="overview-grid">
              <!-- Quick Stats -->
              <p-card class="stats-card">
                <ng-template pTemplate="header"><h3>Header</h3><ng-template pTemplate="content">
                  <div class="stats-grid">
                    <div class="stat-item">
                      <span class="stat-value">{{ (activeGoals$ | async)?.length || 0 }}</span>
                      <span class="stat-label">Active Goals</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-value">{{ (pendingTasks$ | async)?.length || 0 }}</span>
                      <span class="stat-label">Pending Tasks</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-value">{{ (upcomingShifts$ | async)?.length || 0 }}</span>
                      <span class="stat-label">Upcoming Shifts</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-value">{{ (pendingTimeOff$ | async)?.length || 0 }}</span>
                      <span class="stat-label">Pending Time Off</span>
                    </div></ng-template></p-card>

              <!-- Recent Activity -->
              <p-card class="activity-card">
                <ng-template pTemplate="header"><h3>Header</h3><ng-template pTemplate="content">
                  <mat-list>
                    <mat-list-item *ngFor="let activity of recentActivity">
                      <i class="pi pi-circle"></i>
                      <div matListItemTitle>{{ activity.description }}</div>
                      <div matListItemLine>{{ activity.timestamp | date:'short' }}</div>
                    </mat-list-item>
                  </mat-list</p-card>
            </div>
        </p-tabPanel>

        <!-- Goals Tab -->
        <p-tabPanel>
          <ng-template mat-tab-label>
            Goals
            <span class="tab-badge" *ngIf="(activeGoals$ | async)?.length as count">
              ({{ count }})
            </span></ng-template>
          <div class="tab-content">
            <div class="section-header">
              <h3>Goals & Objectives</h3>
              <p-button severity="primary" *ngIf="canEdit" (click)="createGoal()">
                <i class="pi pi-plus"></i>
                Add Goal
              </p-button>
            </div>

            <div class="goals-grid">
              <p-card *ngFor="let goal of goals$ | async" class="goal-card">
                <ng-template pTemplate="header"><h3>
                  <p>Content</p><ng-template pTemplate="content">
                  <p>{{ goal.description }}</p>
                  <div class="goal-progress">
                    <div class="progress-info">
                      <span>Progress: {{ goal.progress }}%</span>
                      <span class="due-date">Due: {{ goal.targetDate | date:'mediumDate' }}</span>
                    </div>
                    <p-progressBar mode="determinate" [value]="goal.progress"></p-progressBar>
                  </div>
                  <div class="goal-status">
                    <p-chip [class]="'status-' + goal.status">{{ goal.status | titlecase }}</p-chip>
                    <p-chip [class]="'priority-' + goal.priority">{{ goal.priority | titlecase }}</p-chip>
                  </div><p-card-actions *ngIf="canEdit">
                  <p-button (click)="editGoal(goal)">Edit</p-button>
                  <p-button (click)="viewGoalDetails(goal)">Details</p-button></ng-template></p-card>
            </div>
        </p-tabPanel>

        <!-- Tasks Tab -->
        <p-tabPanel>
          <ng-template mat-tab-label>
            Tasks
            <span class="tab-badge" *ngIf="(pendingTasks$ | async)?.length as count">
              ({{ count }})
            </span><div class="tab-content">
            <div class="section-header">
              <h3>Tasks & Assignments</h3>
              <p-button severity="primary" *ngIf="canEdit" (click)="createTask()">
                <i class="pi pi-plus"></i>
                Assign Task
              </p-button>
            </div>

            <div class="tasks-list">
              <p-card *ngFor="let task of tasks$ | async" class="task-card">
                <ng-template pTemplate="content">
                  <div class="task-header">
                    <h4>{{ task.title }}</h4>
                    <div class="task-meta">
                      <p-chip [class]="'status-' + task.status">{{ task.status | titlecase }}</p-chip>
                      <p-chip [class]="'priority-' + task.priority">{{ task.priority | titlecase }}</p-chip>
                    </div>
                  <p *ngIf="task.description">{{ task.description }}</p>
                  <div class="task-details">
                    <div class="task-info">
                      <i class="pi pi-clock"></i>
                      <span *ngIf="task.dueDate">Due: {{ task.dueDate | date:'mediumDate' }}</span>
                      <span *ngIf="!task.dueDate">No due date</span>
                    </div>
                    <div class="task-info" *ngIf="task.estimatedHours">
                      <i class="pi pi-circle"></i>
                      <span>{{ task.estimatedHours }}h estimated</span>
                    </div><p-card-actions *ngIf="canEdit">
                  <p-button (click)="editTask(task)">Edit</p-button>
                  <p-button (click)="viewTaskDetails(task)">Details</p-button>
                  <p-button *ngIf="task.status !== 'completed'" (click)="markTaskComplete(task)">
                    Complete
                  </p-button></ng-template></p-card>
            </div>
          </div>
        </p-tabPanel>

        <!-- Schedule Tab -->
        <p-tabPanel label="Schedule">
          <div class="tab-content">
            <div class="section-header">
              <h3>Work Schedule</h3>
              <p-button severity="primary" *ngIf="canEdit" (click)="viewFullCalendar()">
                <i class="pi pi-circle"></i>
                Full Calendar
              </p-button>
            </div>

            <!-- Personal Calendar Component will go here -->
            <p-card class="schedule-card">
              <ng-template pTemplate="content">
                <p>Personal calendar integration coming soon...</p>
                <!-- This will be replaced with a mini FullCalendar component --</p-card>
          </div>
        </p-tabPanel>

        <!-- Time Management Tab -->
        <p-tabPanel label="Time Management">
          <div class="tab-content">
            <div class="time-management-grid">
              <!-- Time Off Requests -->
              <p-card class="time-off-card">
                <ng-template pTemplate="header"><h3>
                  <p-button text="true" *ngIf="canEdit" (click)="requestTimeOff()">
                    <i class="pi pi-plus"></i>
                  </p-button><ng-template pTemplate="content">
                  <mat-list>
                    <mat-list-item *ngFor="let request of timeOffRequests$ | async">
                      <div matListItemTitle>{{ request.type | titlecase }}</div>
                      <div matListItemLine>
                        {{ request.startDate | date:'mediumDate' }} - {{ request.endDate | date:'mediumDate' }}
                      </div>
                      <p-chip matListItemMeta [class]="'status-' + request.status">
                        {{ request.status | titlecase }}
                      </p-chip>
                    </mat-list-item>
                  </mat-list</p-card>

              <!-- Recent Time Entries -->
              <p-card class="time-entries-card">
                <ng-template pTemplate="header"><h3>Header</h3><ng-template pTemplate="content">
                  <mat-list>
                    <mat-list-item *ngFor="let entry of recentTimeEntries$ | async">
                      <i class="pi pi-circle"></i>
                      <div matListItemTitle>{{ entry.type | titlecase }}</div>
                      <div matListItemLine>{{ entry.timestamp | date:'medium' }}</div>
                    </mat-list-item>
                  </mat-list</ng-template></p-card>
            </div>
        </p-tabPanel>
      </p-tabView>

      <!-- Action Menus -->
      <p-menu #avatarMenu="matMenu">
        <button mat-menu-item (click)="uploadAvatar()">
          <i class="pi pi-circle"></i>
          Upload Photo
        </p-button>
        <button mat-menu-item (click)="removeAvatar()" *ngIf="staff.avatar">
          <i class="pi pi-trash"></i>
          Remove Photo
        </p-button>
      </p-menu>

      <p-menu #actionsMenu="matMenu">
        <button mat-menu-item (click)="viewFullProfile()">
          <i class="pi pi-user"></i>
          View Full Profile
        </p-button>
        <button mat-menu-item (click)="exportProfile()">
          <i class="pi pi-circle"></i>
          Export Profile
        </p-button>
        <p-divider></p-divider>
        <button mat-menu-item (click)="deactivateStaff()" *ngIf="staff.status === 'active'">
          <i class="pi pi-circle"></i>
          Deactivate
        </p-button>
      </p-menu>
    </div>
  `,
  styleUrls: ['./staff-profile.component.scss']
})
export class StaffProfileComponent implements OnInit {
  @Input() staffId?: string;

  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private staffService = inject(StaffFirestoreService);
  private calendarService = inject(CalendarService);
  private authService = inject(AuthService);

  staffMember$!: Observable<StaffMember | null>;
  goals$!: Observable<StaffGoalExtended[]>;
  tasks$!: Observable<StaffTask[]>;
  timeOffRequests$!: Observable<TimeOffRequest[]>;
  recentTimeEntries$!: Observable<TimeEntry[]>;
  upcomingShifts$!: Observable<CalendarEvent[]>;

  // Computed observables
  activeGoals$!: Observable<StaffGoalExtended[]>;
  pendingTasks$!: Observable<StaffTask[]>;
  pendingTimeOff$!: Observable<TimeOffRequest[]>;

  canEdit = false;
  recentActivity: any[] = [];

  ngOnInit(): void {
    // Get staff ID from route or input
    const staffIdFromRoute = this.route.snapshot.paramMap.get('id');
    const currentStaffId = this.staffId || staffIdFromRoute;

    if (!currentStaffId) {
      this.router.navigate(['/staff']);
      return;
    }

    // Initialize observables
    this.initializeData(currentStaffId);

    // Check permissions
    this.checkEditPermissions(currentStaffId);
  }

  private initializeData(staffId: string): void {
    // Staff member data
    this.staffMember$ = this.staffService.subscribeToStaffMember(staffId).pipe(shareReplay(1));

    // Goals data
    this.goals$ = this.staffService.subscribeToStaffGoals(staffId).pipe(shareReplay(1));
    this.activeGoals$ = this.goals$.pipe(
      map(goals => goals.filter(g => g.status === 'in-progress' || g.status === 'not-started'))
    );

    // Tasks data
    this.tasks$ = this.staffService.subscribeToStaffTasks(staffId).pipe(shareReplay(1));
    this.pendingTasks$ = this.tasks$.pipe(
      map(tasks => tasks.filter(t => t.status === 'pending' || t.status === 'in-progress'))
    );

    // Time management data
    this.timeOffRequests$ = this.staffService.getTimeOffRequestsByStaff(staffId).pipe(shareReplay(1));
    this.pendingTimeOff$ = this.timeOffRequests$.pipe(
      map(requests => requests.filter(r => r.status === 'pending'))
    );

    // Recent time entries (last 10)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    this.recentTimeEntries$ = this.staffService.getTimeEntriesByStaff(staffId, thirtyDaysAgo).pipe(
      map(entries => entries.slice(0, 10)),
      shareReplay(1)
    );

    // Upcoming shifts (next 7 days)
    const today = new Date();
    const nextWeek = new Date();
    nextWeek.setDate(today.getDate() + 7);
    this.upcomingShifts$ = this.calendarService.getEventsByStaff(staffId, today, nextWeek).pipe(
      map(events => events.filter(e => e.type === 'shift')),
      shareReplay(1)
    );
  }

  private checkEditPermissions(staffId: string): void {
    this.authService.userProfile$.subscribe(profile => {
      if (profile) {
        // Can edit if it's their own profile or if they're admin/manager
        this.canEdit = profile.staffId === staffId ||
                      profile.role === 'admin' ||
                      profile.role === 'manager';
      }
    });
  }

  // Action methods
  editProfile(): void {
    this.router.navigate(['/staff/edit', this.staffId || this.route.snapshot.paramMap.get('id')]);
  }

  createGoal(): void {
    this.router.navigate(['/goals/create'], {
      queryParams: {
        assignedTo: this.staffId || this.route.snapshot.paramMap.get('id'),
        returnUrl: this.router.url
      }
    });
  }

  editGoal(goal: StaffGoalExtended): void {
    this.router.navigate(['/goals/edit', goal.id]);
  }

  viewGoalDetails(goal: StaffGoalExtended): void {
    this.router.navigate(['/goals/details', goal.id]);
  }

  createTask(): void {
    // Navigate to task creation
  }

  editTask(task: StaffTask): void {
    // Navigate to task editing
  }

  viewTaskDetails(task: StaffTask): void {
    // Open task details dialog
  }

  markTaskComplete(task: StaffTask): void {
    this.staffService.updateTask(task.id, {
      status: 'completed',
      completedDate: new Date()
    }).subscribe();
  }

  viewFullCalendar(): void {
    this.router.navigate(['/calendar'], {
      queryParams: { staffId: this.staffId || this.route.snapshot.paramMap.get('id') }
    });
  }

  requestTimeOff(): void {
    // Open time off request dialog
  }

  uploadAvatar(): void {
    // Implement avatar upload
  }

  removeAvatar(): void {
    // Remove avatar
  }

  viewFullProfile(): void {
    // Navigate to full profile view
  }

  exportProfile(): void {
    // Export profile data
  }

  deactivateStaff(): void {
    // Deactivate staff member
  }

  // Utility methods
  getActivityIcon(type: string): string {
    const icons: { [key: string]: string } = {
      'login': 'login',
      'schedule-change': 'schedule',
      'task-completed': 'check_circle',
      'message-sent': 'message',
      'profile-updated': 'person'
    };
    return icons[type] || 'info';
  }

  getTimeEntryIcon(type: string): string {
    const icons: { [key: string]: string } = {
      'clock-in': 'login',
      'clock-out': 'logout',
      'break-start': 'pause',
      'break-end': 'play_arrow',
      'lunch-start': 'restaurant',
      'lunch-end': 'restaurant'
    };
    return icons[type] || 'schedule';
  }
}
