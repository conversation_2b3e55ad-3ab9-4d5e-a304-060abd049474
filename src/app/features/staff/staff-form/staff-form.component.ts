import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { CardModule } from 'primeng/card';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';

import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { CalendarModule } from 'primeng/calendar';

import { ToastModule } from 'primeng/toast';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { StaffService } from '../services/staff.service';
import { StaffMember } from '../models/staff.model';
import { AuthService, UserProfile } from '../../../core/auth/auth.service';
import { Observable, of, take } from 'rxjs';

@Component({
  selector: 'app-staff-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CardModule,
    ButtonModule, InputTextModule,
    DropdownModule,
    CalendarModule,
    ToastModule,
    ProgressSpinnerModule
  ],
  template: `
    <div class="staff-form-container">
      <p-card>
        <ng-template pTemplate="header">
          <h3>
            <i class="pi pi-circle"></i>
            {{ isEditMode ? 'Edit Staff Profile' : 'Add New Staff Member' }}
          <ng-template pTemplate="content">
          <form [formGroup]="staffForm" (ngSubmit)="onSubmit()" *ngIf="!loading">

            <!-- Basic Information Section -->
            <div class="form-section">
              <h3>Basic Information</h3>

              <div class="form-row">
                <div class="p-field" appearance="outline">
                  <label>First Name</label>
                  <input pInputText formControlName="firstName" required>
                  <mat-error *ngIf="staffForm.get('firstName')?.hasError('required')">
                    First name is required
                  </mat-error>
                </div>

                <div class="p-field" appearance="outline">
                  <label>Last Name</label>
                  <input pInputText formControlName="lastName" required>
                  <mat-error *ngIf="staffForm.get('lastName')?.hasError('required')">
                    Last name is required
                  </mat-error>
                </div>

              <div class="form-row">
                <div class="p-field" appearance="outline">
                  <label>Email</label>
                  <input pInputText type="email" formControlName="email" required>
                  <mat-error *ngIf="staffForm.get('email')?.hasError('required')">
                    Email is required
                  </mat-error>
                  <mat-error *ngIf="staffForm.get('email')?.hasError('email')">
                    Please enter a valid email
                  </mat-error>
                </div>

                <div class="p-field" appearance="outline">
                  <label>Phone</label>
                  <input pInputText formControlName="phone" required>
                  <mat-error *ngIf="staffForm.get('phone')?.hasError('required')">
                    Phone is required
                  </mat-error>
                </div>

              <div class="form-row">
                <div class="p-field" appearance="outline">
                  <label>Position</label>
                  <input pInputText formControlName="position" required>
                  <mat-error *ngIf="staffForm.get('position')?.hasError('required')">
                    Position is required
                  </mat-error>
                </div>

                <div class="p-field" appearance="outline">
                  <label>Department</label>
                  <input pInputText formControlName="department" required>
                  <mat-error *ngIf="staffForm.get('department')?.hasError('required')">
                    Department is required
                  </mat-error>
                </div>

              <div class="form-row">
                <div class="p-field" appearance="outline">
                  <label>Employment Type</label>
                  <p-dropdown formControlName="employmentType" required [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
                  <mat-error *ngIf="staffForm.get('employmentType')?.hasError('required')">
                    Employment type is required
                  </mat-error>
                </div>

                <div class="p-field" appearance="outline">
                  <label>Status</label>
                  <p-dropdown formControlName="status" required [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
                  <mat-error *ngIf="staffForm.get('status')?.hasError('required')">
                    Status is required
                  </mat-error>
                </div>

              <div class="form-row">
                <div class="p-field" appearance="outline">
                  <label>Hire Date</label>
                  <input pInputText  formControlName="hireDate" required>
                  
                  
                  <mat-error *ngIf="staffForm.get('hireDate')?.hasError('required')">
                    Hire date is required
                  </mat-error>
                </div>

                <div class="p-field" appearance="outline">
                  <label>Date of Birth</label>
                  <input pInputText  formControlName="dateOfBirth">
                  
                  
                </div>

            <!-- Bio Section -->
            <div class="form-section">
              <h3>Bio</h3>
              <div class="p-field" appearance="outline" class="full-width">
                <label>Bio</label>
                <textarea pInputText formControlName="bio" rows="4"
                         placeholder="Tell us about yourself..."></textarea>
              </div>

          </form>

          <!-- Loading Spinner -->
          <div *ngIf="loading" class="loading-container">
            <p-progressSpinner></p-progressSpinner>
            <p>Loading staff information...</p>
          </div><p-card-actions align="end">
          <p-button type="button" (click)="onCancel()">
            <i class="pi pi-times"></i>
            Cancel
          </p-button>
          <p-button severity="primary"
                  [disabled]="staffForm.invalid || loading"
                  (click)="onSubmit()">
            <i class="pi pi-save"></i>
            {{ isEditMode ? 'Update' : 'Create' }}
          </p-button>
        </ng-template>
      </p-card>
    </div>
  `,
  styles: [`
    .staff-form-container {
      padding: 24px;
      max-width: 800px;
      margin: 0 auto;
    }

    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .form-section {
      margin-bottom: 32px;
    }

    .form-section h3 {
      margin: 0 0 16px 0;
      color: rgba(0, 0, 0, 0.87);
      font-weight: 500;
    }

    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
    }

    .form-row mat-form-field {
      flex: 1;
    }

    .full-width {
      width: 100%;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 40px;
      gap: 16px;
    }

    mat-card-actions {
      padding: 16px 24px;
      gap: 8px;
    }

    @media (max-width: 768px) {
      .form-row {
        flex-direction: column;
        gap: 8px;
      }

      .staff-form-container {
        padding: 16px;
      }
    }
  `]
})
export class StaffFormComponent implements OnInit {
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private staffService = inject(StaffService);
  private authService = inject(AuthService);
  private messageService = inject(MessageService);

  staffForm!: FormGroup;
  loading = false;
  isEditMode = false;
  staffId: string | null = null;

  ngOnInit(): void {
    this.initializeForm();
    this.checkEditMode();
  }

  private initializeForm(): void {
    this.staffForm = this.fb.group({
      firstName: ['', [Validators.required]],
      lastName: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required]],
      position: ['', [Validators.required]],
      department: ['', [Validators.required]],
      employmentType: ['full-time', [Validators.required]],
      status: ['active', [Validators.required]],
      hireDate: ['', [Validators.required]],
      dateOfBirth: [''],
      bio: ['']
    });
  }

  private checkEditMode(): void {
    this.staffId = this.route.snapshot.paramMap.get('id');
    this.isEditMode = !!this.staffId;

    if (this.isEditMode && this.staffId) {
      this.loadStaffMember(this.staffId);
    }
  }

  private loadStaffMember(id: string): void {
    this.loading = true;

    // Load user profile using AuthService (handles injection context properly)
    this.authService.userProfile$.pipe(take(1)).subscribe(profile => {
      if (profile) {
        // Convert user profile to staff member format for the form
        const staffData: Partial<StaffMember> = {
          id: profile.uid,
          firstName: (profile as any).firstName || this.extractFirstName(profile.displayName || ''),
          lastName: (profile as any).lastName || this.extractLastName(profile.displayName || ''),
          email: profile.email,
          phone: (profile as any).phone || '',
          position: (profile as any).position || '',
          department: (profile as any).department || '',
          employmentType: (profile as any).employmentType || 'full-time',
          status: (profile as any).status || 'active',
          hireDate: (profile as any).hireDate || profile.createdAt,
          dateOfBirth: (profile as any).dateOfBirth,
          bio: (profile as any).bio || ''
        };

        this.populateForm(staffData as StaffMember);
        this.loading = false;
      } else {
        // No user profile found, use basic user data
        this.loadFromUserProfile(id);
      }
    });
  }

  private loadFromUserProfile(id: string): void {
    // Get current user profile and use it for staff data
    this.authService.userProfile$.pipe(take(1)).subscribe(profile => {
      if (profile) {
        // Convert user profile to staff member format
        const staffData: Partial<StaffMember> = {
          id: id,
          firstName: this.extractFirstName(profile.displayName),
          lastName: this.extractLastName(profile.displayName),
          email: profile.email,
          phone: '', // Will need to be filled by user
          position: '', // Will need to be filled by user
          department: '', // Will need to be filled by user
          employmentType: 'full-time',
          status: 'active',
          hireDate: profile.createdAt,
          dateOfBirth: undefined,
          bio: ''
        };

        this.populateForm(staffData as StaffMember);
        this.loading = false;
      } else {
        // Fallback: redirect to login if no profile
        this.router.navigate(['/auth/login']);
        this.loading = false;
      }
    });
  }

  private extractFirstName(displayName: string): string {
    return displayName.split(' ')[0] || '';
  }

  private extractLastName(displayName: string): string {
    const parts = displayName.split(' ');
    return parts.length > 1 ? parts.slice(1).join(' ') : '';
  }

  private populateForm(staff: StaffMember): void {
    this.staffForm.patchValue({
      firstName: staff.firstName,
      lastName: staff.lastName,
      email: staff.email,
      phone: staff.phone,
      position: staff.position,
      department: staff.department,
      employmentType: staff.employmentType,
      status: staff.status,
      hireDate: staff.hireDate,
      dateOfBirth: staff.dateOfBirth,
      bio: staff.bio
    });
  }

  onSubmit(): void {
    if (this.staffForm.valid) {
      this.loading = true;
      const formData = this.staffForm.value;

      if (this.isEditMode && this.staffId) {
        this.updateStaffMember(this.staffId, formData);
      } else {
        this.createStaffMember(formData);
      }
    }
  }

  private createStaffMember(data: any): void {
    console.log('Creating staff member:', data);

    // Get current user to create staff profile
    this.authService.user$.pipe(take(1)).subscribe(user => {
      if (user) {
        const staffData = {
          ...data,
          uid: user.uid,
          email: user.email,
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: user.uid
        };

        // Save to Firestore using staff service
        this.staffService.createStaffMember(staffData).subscribe({
          next: (result) => {
            this.messageService.add({ severity: 'info', summary: 'Staff profile created successfully' });
            this.router.navigate(['/staff']);
            this.loading = false;
          },
          error: (error) => {
            console.error('Error creating staff member:', error);
            this.messageService.add({ severity: 'info', summary: 'Error creating staff profile. Please try again.' });
            this.loading = false;
          }
        });
      } else {
        this.messageService.add({ severity: 'info', summary: 'User not authenticated. Please login again.' });
        this.router.navigate(['/auth/login']);
        this.loading = false;
      }
    });
  }

  private updateStaffMember(id: string, data: any): void {
    console.log('Updating staff member:', id, data);

    // Get current user to update staff profile
    this.authService.user$.pipe(take(1)).subscribe(user => {
      if (user) {
        // Prepare profile updates
        const profileUpdates = {
          displayName: `${data.firstName} ${data.lastName}`,
          firstName: data.firstName,
          lastName: data.lastName,
          email: data.email,
          phone: data.phone,
          position: data.position,
          department: data.department,
          employmentType: data.employmentType,
          status: data.status,
          hireDate: data.hireDate,
          dateOfBirth: data.dateOfBirth,
          bio: data.bio,
          updatedAt: new Date(),
          updatedBy: user.uid
        };

        // Use AuthService to update profile (handles injection context properly)
        this.authService.updateUserProfile(user.uid, profileUpdates).subscribe({
          next: () => {
            console.log('✅ User profile updated successfully');
            this.messageService.add({ severity: 'info', summary: 'Staff profile updated successfully' });
            this.router.navigate(['/settings']);
            this.loading = false;
          },
          error: (error) => {
            console.error('❌ Error updating staff profile:', error);
            this.messageService.add({ severity: 'info', summary: 'Error updating staff profile. Please try again.' });
            this.loading = false;
          }
        });
      } else {
        this.messageService.add({ severity: 'info', summary: 'User not authenticated. Please login again.' });
        this.router.navigate(['/auth/login']);
        this.loading = false;
      }
    });
  }

  onCancel(): void {
    if (this.isEditMode && this.staffId) {
      this.router.navigate(['/staff/profile', this.staffId]);
    } else {
      this.router.navigate(['/staff']);
    }
  }
}
