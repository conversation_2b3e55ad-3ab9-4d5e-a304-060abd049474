import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'app-staff-detail',
  standalone: true,
  imports: [
    CommonModule, CardModule, ButtonModule
  ],
  template: `
    <div class="staff-detail-container">
      <p-card>
        <ng-template pTemplate="header">
          <h3>
            <i class="pi pi-user"></i>
            Staff Details
          <ng-template pTemplate="content">
          <p>Staff detail view coming soon!</p></ng-template>
      </p-card>
    </div>
  `,
  styles: [`
    .staff-detail-container {
      padding: 24px;
    }
    mat-card-title {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  `]
})
export class StaffDetailComponent {}
