import { Component, OnInit, inject, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Observable, combineLatest } from 'rxjs';
import { map, shareReplay } from 'rxjs/operators';

// Angular Material
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';

import { ToolbarModule } from 'primeng/toolbar';
import { DropdownModule } from 'primeng/dropdown';
import { MultiSelectModule } from 'primeng/multiselect';
import { MenuModule } from 'primeng/menu';
import { ToastModule } from 'primeng/toast';
import { FormsModule } from '@angular/forms';

// FullCalendar
import { FullCalendarModule } from '@fullcalendar/angular';
import { CalendarOptions, EventApi, EventInput } from '@fullcalendar/core';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import listPlugin from '@fullcalendar/list';
import interactionPlugin from '@fullcalendar/interaction';

// Services
import { CalendarService } from './services/calendar.service';
import { StaffFirestoreService } from '../staff/services/staff-firestore.service';
import { AuthService } from '../../core/auth/auth.service';

// Models
import { CalendarEvent, Shift } from './models/calendar.model';
import { StaffMember } from '../staff/models/staff.model';

@Component({
  selector: 'app-calendar',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    CardModule,
    ButtonModule,
    ToolbarModule,
    DropdownModule,
    MultiSelectModule,
    MenuModule,
    ToastModule,
    FullCalendarModule
  ],
  template: `
    <div class="calendar-container"><!-- Calendar Toolbar -->
      <p-toolbar class="calendar-toolbar">
        <ng-template pTemplate="left">
          <div class="toolbar-left">
            <h2>
              <i class="pi pi-calendar"></i>
              Staff Calendar & Scheduling
            </h2>
          </div>
        </ng-template>

        <ng-template pTemplate="center">
          <div class="toolbar-center">
            <div class="p-field view-selector">
              <label>View</label>
              <p-dropdown [(ngModel)]="currentView" (onChange)="changeView($event.value)" [options]="viewOptions" optionLabel="label" optionValue="value"></p-dropdown>
            </div>

            <div class="p-field staff-filter" *ngIf="staff$ | async as staffList">
              <label>Filter by Staff</label>
              <p-multiSelect [(ngModel)]="selectedStaffIds" (onChange)="filterByStaff()" [options]="staffOptions" optionLabel="label" optionValue="value"></p-multiSelect>
            </div>
        </ng-template>

        <ng-template pTemplate="right">
          <div class="toolbar-right">
            <p-button severity="primary" (click)="showCreateMenu($event)">
              <i class="pi pi-plus"></i>
              Create
            </p-button>

            <p-button [text]="true" (click)="showOptionsMenu($event)">
              <i class="pi pi-ellipsis-v"></i>
            </p-button>
          </div>
        </ng-template>
      </p-toolbar>

      <!-- Calendar Component -->
      <div class="calendar-wrapper">
        <full-calendar
          #calendar
          [options]="calendarOptions">
        </full-calendar>
      </div>

      <!-- Staff Sidebar (for drag and drop) -->
      <div class="staff-sidebar" [class.collapsed]="sidebarCollapsed">
        <div class="sidebar-header">
          <h3>Staff</h3>
          <p-button text="true" (click)="toggleSidebar()">
            <i class="pi pi-circle"></i>
          </p-button>
        </div>

        <div class="staff-list" *ngIf="!sidebarCollapsed">
          <div
            *ngFor="let staff of staff$ | async"
            class="staff-item"
            [class.available]="isStaffAvailable(staff)"
            draggable="true"
            (dragstart)="onStaffDragStart($event, staff)">
            <div class="staff-avatar">
              <i class="pi pi-circle"></i>
              <img *ngIf="staff.avatar" [src]="staff.avatar" [alt]="staff.firstName">
            </div>
            <div class="staff-info">
              <div class="staff-name">{{ staff.firstName }} {{ staff.lastName }}</div>
              <div class="staff-position">{{ staff.position }}</div>
              <div class="staff-status" [class]="'status-' + staff.status">
                {{ staff.status }}</div>

      <!-- Create Menu -->
      <p-menu #createMenu [popup]="true" [model]="createMenuItems"></p-menu>

      <!-- Options Menu -->
      <p-menu #optionsMenu [popup]="true" [model]="optionsMenuItems"></p-menu>
    </div>
  `,
  styles: [`
    .calendar-container {
      display: flex;
      flex-direction: column;
      height: 100vh;
      position: relative;
    }

    .calendar-toolbar {
      background: white;
      color: #333;
      border-bottom: 1px solid #e0e0e0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 24px;
      min-height: 64px;

      .toolbar-left h2 {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0;
        font-size: 1.25rem;
        font-weight: 500;
      }

      .toolbar-center {
        display: flex;
        gap: 16px;
        align-items: center;

        .view-selector,
        .staff-filter {
          min-width: 120px;
        }
      }

      .toolbar-right {
        display: flex;
        gap: 8px;
        align-items: center;
      }
    }

    .calendar-wrapper {
      flex: 1;
      padding: 24px;
      padding-right: 320px;
      transition: padding-right 0.3s ease;

      &.sidebar-collapsed {
        padding-right: 80px;
      }
    }

    .staff-sidebar {
      position: fixed;
      top: 64px;
      right: 0;
      width: 300px;
      height: calc(100vh - 64px);
      background: white;
      border-left: 1px solid #e0e0e0;
      transition: width 0.3s ease;
      z-index: 100;

      &.collapsed {
        width: 60px;
      }

      .sidebar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #e0e0e0;

        h3 {
          margin: 0;
          font-size: 1.125rem;
          font-weight: 500;
        }
      }

      .staff-list {
        padding: 16px;
        overflow-y: auto;
        height: calc(100% - 64px);

        .staff-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px;
          border-radius: 8px;
          margin-bottom: 8px;
          cursor: grab;
          border: 2px solid transparent;
          transition: all 0.2s ease;

          &:hover {
            background: #f5f5f5;
          }

          &.available {
            border-color: #4caf50;
          }

          &:active {
            cursor: grabbing;
          }

          .staff-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            background: #f0f0f0;
            display: flex;
            align-items: center;
            justify-content: center;

            mat-icon {
              font-size: 32px;
              width: 32px;
              height: 32px;
              color: #9e9e9e;
            }

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .staff-info {
            flex: 1;

            .staff-name {
              font-weight: 500;
              font-size: 0.875rem;
              margin-bottom: 2px;
            }

            .staff-position {
              font-size: 0.75rem;
              color: #666;
              margin-bottom: 2px;
            }

            .staff-status {
              font-size: 0.75rem;
              padding: 2px 6px;
              border-radius: 10px;
              display: inline-block;

              &.status-active {
                background: #e8f5e8;
                color: #2e7d32;
              }

              &.status-inactive {
                background: #ffebee;
                color: #c62828;
              }

              &.status-on-leave {
                background: #fff3e0;
                color: #ef6c00;
              }
            }
          }
        }
      }
    }

    // FullCalendar customizations
    ::ng-deep {
      .fc {
        height: 100%;
      }

      .fc-event {
        border-radius: 4px;
        border: none;
        padding: 2px 4px;
        font-size: 0.75rem;
      }

      .fc-event-title {
        font-weight: 500;
      }

      .fc-daygrid-event {
        margin: 1px 0;
      }

      .fc-timegrid-event {
        border-radius: 4px;
      }

      // Event type colors
      .fc-event.event-shift {
        background-color: #1976d2;
        border-color: #1976d2;
      }

      .fc-event.event-meeting {
        background-color: #388e3c;
        border-color: #388e3c;
      }

      .fc-event.event-training {
        background-color: #f57c00;
        border-color: #f57c00;
      }

      .fc-event.event-time-off {
        background-color: #d32f2f;
        border-color: #d32f2f;
      }

      .fc-event.event-break {
        background-color: #7b1fa2;
        border-color: #7b1fa2;
      }
    }

    @media (max-width: 768px) {
      .calendar-toolbar {
        flex-direction: column;
        gap: 16px;
        padding: 16px;
        min-height: auto;

        .toolbar-center {
          order: 3;
          width: 100%;
          justify-content: center;
        }
      }

      .calendar-wrapper {
        padding: 16px;
        padding-right: 16px;
      }

      .staff-sidebar {
        display: none;
      }
    }
  `]
})
export class CalendarComponent implements OnInit {
  @ViewChild('calendar') calendarComponent!: ElementRef;

  private calendarService = inject(CalendarService);
  private staffService = inject(StaffFirestoreService);
  private authService = inject(AuthService);

  // Observables
  events$!: Observable<CalendarEvent[]>;
  staff$!: Observable<StaffMember[]>;

  // Calendar configuration
  calendarOptions: CalendarOptions = {
    initialView: 'dayGridMonth',
    plugins: [dayGridPlugin, timeGridPlugin, listPlugin, interactionPlugin],
    headerToolbar: {
      left: 'prev,next today',
      center: 'title',
      right: ''
    },
    editable: true,
    selectable: true,
    selectMirror: true,
    dayMaxEvents: true,
    weekends: true,
    droppable: true,
    eventResizableFromStart: true,
    eventDurationEditable: true,

    // Event handlers
    select: this.handleDateSelect.bind(this),
    eventClick: this.handleEventClick.bind(this),
    eventDrop: this.handleEventDrop.bind(this),
    eventResize: this.handleEventResize.bind(this),
    drop: this.handleStaffDrop.bind(this),

    // Event rendering
    eventClassNames: (arg) => {
      return [`event-${arg.event.extendedProps['type']}`];
    },

    eventContent: (arg) => {
      const event = arg.event;
      const type = event.extendedProps['type'];
      const assignedStaff = event.extendedProps['assignedStaff'] || [];

      return {
        html: `
          <div class="fc-event-main">
            <div class="fc-event-title">${event.title}</div>
            <div class="fc-event-staff">${assignedStaff.length} staff</div>
        `
      };
    }
  };

  // Component state
  currentView = 'dayGridMonth';
  selectedStaffIds: string[] = [];
  sidebarCollapsed = false;
  currentBusinessId = '';

  // Dropdown options
  get viewOptions() {
    return [
      { label: 'Month', value: 'dayGridMonth' },
      { label: 'Week', value: 'timeGridWeek' },
      { label: 'Day', value: 'timeGridDay' },
      { label: 'List', value: 'listWeek' }
    ];
  }

  get staffOptions() {
    // This will be populated from staff$ observable
    return [];
  }

  // Menu items
  get createMenuItems() {
    return [
      {
        label: 'Create Shift',
        icon: 'pi pi-clock',
        command: () => this.createShift()
      },
      {
        label: 'Schedule Meeting',
        icon: 'pi pi-users',
        command: () => this.createMeeting()
      },
      {
        label: 'Schedule Training',
        icon: 'pi pi-book',
        command: () => this.createTraining()
      },
      {
        label: 'Time Off',
        icon: 'pi pi-calendar-times',
        command: () => this.createTimeOff()
      }
    ];
  }

  get optionsMenuItems() {
    return [
      {
        label: 'Export Calendar',
        icon: 'pi pi-download',
        command: () => this.exportCalendar()
      },
      {
        label: 'Import Calendar',
        icon: 'pi pi-upload',
        command: () => this.importCalendar()
      },
      {
        label: 'Calendar Settings',
        icon: 'pi pi-cog',
        command: () => this.calendarSettings()
      },
      {
        label: 'View Analytics',
        icon: 'pi pi-chart-bar',
        command: () => this.viewAnalytics()
      }
    ];
  }

  ngOnInit(): void {
    this.initializeData();
    this.setupCalendarEvents();
  }

  private initializeData(): void {
    // Get current business ID from auth service
    this.authService.userProfile$.subscribe(profile => {
      if (profile?.primaryBusinessId) {
        this.currentBusinessId = profile.primaryBusinessId;
        this.loadCalendarData();
      }
    });

    // Load staff data
    this.staff$ = this.staffService.subscribeToStaff(this.currentBusinessId).pipe(
      shareReplay(1)
    );
  }

  private loadCalendarData(): void {
    // Load events for current month
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    this.events$ = this.calendarService.subscribeToEvents(
      this.currentBusinessId,
      startOfMonth,
      endOfMonth
    ).pipe(shareReplay(1));

    // Update calendar events
    this.events$.subscribe(events => {
      const calendarEvents = this.calendarService.convertToFullCalendarEvents(events);
      this.calendarOptions.events = calendarEvents;
    });
  }

  private setupCalendarEvents(): void {
    // Additional calendar setup if needed
  }

  // Calendar event handlers
  handleDateSelect(selectInfo: any): void {
    const title = prompt('Enter event title:');
    if (title) {
      this.createEventAtDate(selectInfo.start, selectInfo.end, title);
    }
    selectInfo.view.calendar.unselect();
  }

  handleEventClick(clickInfo: any): void {
    // Open event details dialog
    console.log('Event clicked:', clickInfo.event);
  }

  handleEventDrop(dropInfo: any): void {
    // Update event in database
    const event = dropInfo.event;
    this.calendarService.updateEvent(event.id, {
      start: event.start,
      end: event.end
    }).subscribe();
  }

  handleEventResize(resizeInfo: any): void {
    // Update event duration in database
    const event = resizeInfo.event;
    this.calendarService.updateEvent(event.id, {
      start: event.start,
      end: event.end
    }).subscribe();
  }

  handleStaffDrop(dropInfo: any): void {
    // Handle staff member dropped on calendar
    const staffId = dropInfo.draggedEl.dataset['staffId'];
    const date = dropInfo.date;

    if (staffId) {
      this.createShiftForStaff(staffId, date);
    }
  }

  // Staff drag and drop
  onStaffDragStart(event: DragEvent, staff: StaffMember): void {
    if (event.dataTransfer) {
      event.dataTransfer.setData('text/plain', staff.id);
      event.dataTransfer.effectAllowed = 'copy';
    }
  }

  // View management
  changeView(view: string): void {
    this.currentView = view;
    // Update FullCalendar view
  }

  // Menu action methods
  showCreateMenu(event: Event): void {
    // Menu will be shown automatically via [model] binding
  }

  showOptionsMenu(event: Event): void {
    // Menu will be shown automatically via [model] binding
  }

  filterByStaff(): void {
    // Filter events by selected staff
    if (this.selectedStaffIds.includes('all') || this.selectedStaffIds.length === 0) {
      this.loadCalendarData();
    } else {
      // Load filtered events
      this.events$ = combineLatest(
        this.selectedStaffIds.map(staffId =>
          this.calendarService.getEventsByStaff(staffId)
        )
      ).pipe(
        map(eventArrays => eventArrays.flat()),
        shareReplay(1)
      );
    }
  }

  toggleSidebar(): void {
    this.sidebarCollapsed = !this.sidebarCollapsed;
  }

  // Event creation methods
  createEvent(): void {
    // Open create event dialog
  }

  createShift(): void {
    // Open create shift dialog
  }

  createMeeting(): void {
    // Open create meeting dialog
  }

  createTraining(): void {
    // Open create training dialog
  }

  createTimeOff(): void {
    // Open time off dialog
  }

  private createEventAtDate(start: Date, end: Date, title: string): void {
    const newEvent: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'> = {
      title,
      start,
      end,
      type: 'other',
      status: 'scheduled',
      assignedStaff: [],
      createdBy: '', // Get from auth service
      businessId: this.currentBusinessId
    };

    this.calendarService.createEvent(newEvent).subscribe();
  }

  private createShiftForStaff(staffId: string, date: Date): void {
    // Create a shift for the dropped staff member
    const startTime = new Date(date);
    startTime.setHours(9, 0, 0, 0);
    const endTime = new Date(date);
    endTime.setHours(17, 0, 0, 0);

    const newShift: Omit<Shift, 'id' | 'createdAt' | 'updatedAt'> = {
      title: 'New Shift',
      start: startTime,
      end: endTime,
      type: 'shift',
      status: 'scheduled',
      assignedStaff: [staffId],
      createdBy: '', // Get from auth service
      businessId: this.currentBusinessId,
      position: '',
      department: '',
      minimumStaff: 1,
      maximumStaff: 1,
      scheduledHours: 8,
      canBeSwapped: true
    };

    this.calendarService.createShift(newShift).subscribe();
  }

  // Utility methods
  isStaffAvailable(staff: StaffMember): boolean {
    return staff.status === 'active';
  }

  // Menu actions
  exportCalendar(): void {
    // Export calendar functionality
  }

  importCalendar(): void {
    // Import calendar functionality
  }

  calendarSettings(): void {
    // Open calendar settings dialog
  }

  viewAnalytics(): void {
    // Navigate to analytics view
  }
}
