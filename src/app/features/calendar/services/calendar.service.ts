import { Injectable, inject } from '@angular/core';
import { Observable, from, map, switchMap, combineLatest, of } from 'rxjs';
import { where, orderBy, limit as firestoreLimit } from '@angular/fire/firestore';
import { FirestoreBaseService } from '../../../core/services/firestore-base.service';
import { AuthService } from '../../../core/auth/auth.service';
import { 
  CalendarEvent, 
  Shift, 
  ShiftSwapRequest,
  CalendarViewConfig,
  StaffAvailabilitySlot,
  SchedulingRule,
  CalendarStats
} from '../models/calendar.model';

@Injectable({
  providedIn: 'root'
})
export class CalendarService extends FirestoreBaseService {
  private authService = inject(AuthService);
  private readonly EVENTS_COLLECTION = 'calendarEvents';
  private readonly SHIFTS_COLLECTION = 'shifts';
  private readonly SWAP_REQUESTS_COLLECTION = 'shiftSwapRequests';
  private readonly AVAILABILITY_COLLECTION = 'staffAvailability';
  private readonly RULES_COLLECTION = 'schedulingRules';
  private readonly VIEW_CONFIGS_COLLECTION = 'calendarViewConfigs';

  // Calendar Events CRUD
  createEvent(event: Omit<CalendarEvent, 'id' | 'createdAt' | 'updatedAt'>): Observable<CalendarEvent> {
    return from(this.create<CalendarEvent>(this.EVENTS_COLLECTION, event));
  }

  updateEvent(id: string, updates: Partial<Omit<CalendarEvent, 'id' | 'createdAt'>>): Observable<void> {
    return from(this.update<CalendarEvent>(this.EVENTS_COLLECTION, id, updates));
  }

  deleteEvent(id: string): Observable<void> {
    return from(this.delete(this.EVENTS_COLLECTION, id));
  }

  getEventById(id: string): Observable<CalendarEvent | null> {
    return from(this.getById<CalendarEvent>(this.EVENTS_COLLECTION, id));
  }

  getEvents(businessId: string, startDate?: Date, endDate?: Date): Observable<CalendarEvent[]> {
    let constraints = [
      this.createWhereConstraint('businessId', '==', businessId),
      this.createOrderByConstraint('start', 'asc')
    ];

    if (startDate) {
      constraints.push(this.createWhereConstraint('start', '>=', startDate));
    }
    if (endDate) {
      constraints.push(this.createWhereConstraint('end', '<=', endDate));
    }

    return from(this.getAll<CalendarEvent>(this.EVENTS_COLLECTION, constraints));
  }

  getEventsByStaff(staffId: string, startDate?: Date, endDate?: Date): Observable<CalendarEvent[]> {
    let constraints = [
      this.createWhereConstraint('assignedStaff', 'array-contains', staffId),
      this.createOrderByConstraint('start', 'asc')
    ];

    if (startDate) {
      constraints.push(this.createWhereConstraint('start', '>=', startDate));
    }
    if (endDate) {
      constraints.push(this.createWhereConstraint('end', '<=', endDate));
    }

    return from(this.getAll<CalendarEvent>(this.EVENTS_COLLECTION, constraints));
  }

  subscribeToEvents(businessId: string, startDate?: Date, endDate?: Date): Observable<CalendarEvent[]> {
    let constraints = [
      this.createWhereConstraint('businessId', '==', businessId),
      this.createOrderByConstraint('start', 'asc')
    ];

    if (startDate) {
      constraints.push(this.createWhereConstraint('start', '>=', startDate));
    }
    if (endDate) {
      constraints.push(this.createWhereConstraint('end', '<=', endDate));
    }

    return this.subscribeToCollection<CalendarEvent>(this.EVENTS_COLLECTION, constraints);
  }

  subscribeToStaffEvents(staffId: string, startDate?: Date, endDate?: Date): Observable<CalendarEvent[]> {
    let constraints = [
      this.createWhereConstraint('assignedStaff', 'array-contains', staffId),
      this.createOrderByConstraint('start', 'asc')
    ];

    if (startDate) {
      constraints.push(this.createWhereConstraint('start', '>=', startDate));
    }
    if (endDate) {
      constraints.push(this.createWhereConstraint('end', '<=', endDate));
    }

    return this.subscribeToCollection<CalendarEvent>(this.EVENTS_COLLECTION, constraints);
  }

  // Shift Management
  createShift(shift: Omit<Shift, 'id' | 'createdAt' | 'updatedAt'>): Observable<Shift> {
    return from(this.create<Shift>(this.SHIFTS_COLLECTION, shift));
  }

  updateShift(id: string, updates: Partial<Omit<Shift, 'id' | 'createdAt'>>): Observable<void> {
    return from(this.update<Shift>(this.SHIFTS_COLLECTION, id, updates));
  }

  deleteShift(id: string): Observable<void> {
    return from(this.delete(this.SHIFTS_COLLECTION, id));
  }

  getShiftsByStaff(staffId: string, startDate?: Date, endDate?: Date): Observable<Shift[]> {
    let constraints = [
      this.createWhereConstraint('assignedStaff', 'array-contains', staffId),
      this.createOrderByConstraint('start', 'asc')
    ];

    if (startDate) {
      constraints.push(this.createWhereConstraint('start', '>=', startDate));
    }
    if (endDate) {
      constraints.push(this.createWhereConstraint('end', '<=', endDate));
    }

    return from(this.getAll<Shift>(this.SHIFTS_COLLECTION, constraints));
  }

  // Shift Swap Requests
  createShiftSwapRequest(request: Omit<ShiftSwapRequest, 'id' | 'createdAt' | 'updatedAt'>): Observable<ShiftSwapRequest> {
    return from(this.create<ShiftSwapRequest>(this.SWAP_REQUESTS_COLLECTION, request));
  }

  updateShiftSwapRequest(id: string, updates: Partial<Omit<ShiftSwapRequest, 'id' | 'createdAt'>>): Observable<void> {
    return from(this.update<ShiftSwapRequest>(this.SWAP_REQUESTS_COLLECTION, id, updates));
  }

  getShiftSwapRequests(businessId: string, status?: string): Observable<ShiftSwapRequest[]> {
    let constraints = [
      this.createOrderByConstraint('requestedAt', 'desc')
    ];

    if (status) {
      constraints.unshift(this.createWhereConstraint('status', '==', status));
    }

    return from(this.getAll<ShiftSwapRequest>(this.SWAP_REQUESTS_COLLECTION, constraints));
  }

  // Staff Availability
  setStaffAvailability(availability: Omit<StaffAvailabilitySlot, 'id' | 'createdAt' | 'updatedAt'>): Observable<StaffAvailabilitySlot> {
    return from(this.create<StaffAvailabilitySlot>(this.AVAILABILITY_COLLECTION, availability));
  }

  updateStaffAvailability(id: string, updates: Partial<Omit<StaffAvailabilitySlot, 'id' | 'createdAt'>>): Observable<void> {
    return from(this.update<StaffAvailabilitySlot>(this.AVAILABILITY_COLLECTION, id, updates));
  }

  getStaffAvailability(staffId: string, startDate?: Date, endDate?: Date): Observable<StaffAvailabilitySlot[]> {
    let constraints = [
      this.createWhereConstraint('staffId', '==', staffId),
      this.createOrderByConstraint('date', 'asc')
    ];

    if (startDate) {
      constraints.push(this.createWhereConstraint('date', '>=', startDate));
    }
    if (endDate) {
      constraints.push(this.createWhereConstraint('date', '<=', endDate));
    }

    return from(this.getAll<StaffAvailabilitySlot>(this.AVAILABILITY_COLLECTION, constraints));
  }

  // Scheduling Rules
  createSchedulingRule(rule: Omit<SchedulingRule, 'id' | 'createdAt' | 'updatedAt'>): Observable<SchedulingRule> {
    return from(this.create<SchedulingRule>(this.RULES_COLLECTION, rule));
  }

  updateSchedulingRule(id: string, updates: Partial<Omit<SchedulingRule, 'id' | 'createdAt'>>): Observable<void> {
    return from(this.update<SchedulingRule>(this.RULES_COLLECTION, id, updates));
  }

  getSchedulingRules(businessId: string): Observable<SchedulingRule[]> {
    const constraints = [
      this.createWhereConstraint('businessId', '==', businessId),
      this.createWhereConstraint('isActive', '==', true),
      this.createOrderByConstraint('name', 'asc')
    ];

    return from(this.getAll<SchedulingRule>(this.RULES_COLLECTION, constraints));
  }

  // Calendar View Configurations
  saveViewConfig(config: Omit<CalendarViewConfig, 'id' | 'createdAt' | 'updatedAt'>): Observable<CalendarViewConfig> {
    return from(this.create<CalendarViewConfig>(this.VIEW_CONFIGS_COLLECTION, config));
  }

  updateViewConfig(id: string, updates: Partial<Omit<CalendarViewConfig, 'id' | 'createdAt'>>): Observable<void> {
    return from(this.update<CalendarViewConfig>(this.VIEW_CONFIGS_COLLECTION, id, updates));
  }

  getViewConfigs(businessId: string): Observable<CalendarViewConfig[]> {
    const constraints = [
      this.createWhereConstraint('businessId', '==', businessId),
      this.createOrderByConstraint('name', 'asc')
    ];

    return from(this.getAll<CalendarViewConfig>(this.VIEW_CONFIGS_COLLECTION, constraints));
  }

  // Utility Methods
  convertToFullCalendarEvents(events: CalendarEvent[]): any[] {
    return events.map(event => ({
      id: event.id,
      title: event.title,
      start: event.start,
      end: event.end,
      allDay: event.allDay || false,
      backgroundColor: event.backgroundColor || this.getEventTypeColor(event.type),
      borderColor: event.borderColor || this.getEventTypeColor(event.type),
      textColor: event.textColor || '#ffffff',
      extendedProps: {
        type: event.type,
        status: event.status,
        assignedStaff: event.assignedStaff,
        description: event.description,
        location: event.location,
        notes: event.notes,
        priority: event.priority
      }
    });
  }

  private getEventTypeColor(type: string): string {
    const colors: { [key: string]: string } = {
      'shift': '#1976d2',
      'meeting': '#388e3c',
      'training': '#f57c00',
      'time-off': '#d32f2f',
      'break': '#7b1fa2',
      'appointment': '#0097a7',
      'other': '#616161'
    };
    return colors[type] || colors['other'];
  }

  // Analytics and Statistics
  getCalendarStats(businessId: string, startDate: Date, endDate: Date): Observable<CalendarStats> {
    return this.getEvents(businessId, startDate, endDate).pipe(
      map(events => this.calculateStats(events, businessId, startDate, endDate))
    );
  }

  private calculateStats(events: CalendarEvent[], businessId: string, startDate: Date, endDate: Date): CalendarStats {
    const eventsByType: { [type: string]: number } = {};
    let totalScheduledHours = 0;
    let totalActualHours = 0;

    events.forEach(event => {
      eventsByType[event.type] = (eventsByType[event.type] || 0) + 1;
      
      if (event.type === 'shift') {
        const shift = event as Shift;
        totalScheduledHours += shift.scheduledHours;
        totalActualHours += shift.actualHours || 0;
      }
    });

    return {
      businessId,
      period: { start: startDate, end: endDate },
      totalEvents: events.length,
      eventsByType,
      totalScheduledHours,
      totalActualHours,
      attendanceRate: totalScheduledHours > 0 ? (totalActualHours / totalScheduledHours) * 100 : 0,
      noShowRate: 0, // Calculate based on no-show events
      averageShiftLength: 0, // Calculate average
      mostActiveStaff: [], // Calculate from events
      busyDays: [] // Calculate from events
    };
  }
}
