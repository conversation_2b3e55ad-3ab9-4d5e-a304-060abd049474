import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { CardModule } from 'primeng/card';
import { DialogService } from 'primeng/dynamicdialog';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';

import { TabViewModule } from 'primeng/tabview';
import { ListboxModule } from 'primeng/listbox';

import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { ChipModule } from 'primeng/chip';
import { TableModule } from 'primeng/table';

import { ToastModule } from 'primeng/toast';
// import { QRCodeModule } from 'angularx-qrcode';

interface TimeClockDevice {
  id: string;
  name: string;
  location: string;
  url: string;
  status: 'online' | 'offline' | 'maintenance';
  lastSeen: Date;
  version: string;
  settings: {
    allowEarlyClockIn: boolean;
    requireLocation: boolean;
    autoBreaks: boolean;
  };
}

interface StaffPin {
  staffId: string;
  staffName: string;
  pin: string;
  isActive: boolean;
  lastUsed?: Date;
  failedAttempts: number;
  expiresAt?: Date;
}

@Component({
  selector: 'app-timehub-setup',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    CardModule,
    ButtonModule, TabViewModule,
    ListboxModule,
    InputTextModule,
    DropdownModule,
    ToggleButtonModule,
    ChipModule,
    TableModule,
    ToastModule
  ],
  template: `
    <div class="timehub-setup-container">
      <p-card>
        <ng-template pTemplate="header">
          <i class="pi pi-circle"></i>
          <h3>
          </ng-template>
        <ng-template pTemplate="content">
          <p-tabView>

            <!-- Kiosk Devices Tab -->
            <p-tabPanel label="Kiosk Devices">
              <div class="tab-content">
                <div class="section-header">
                  <h3>Time Clock Devices</h3>
                  <p-button severity="primary" (click)="addDevice()">
                    <i class="pi pi-plus"></i>
                    Add Device
                  </p-button>
                </div>

                <div class="devices-grid">
                  <p-card *ngFor="let device of devices" class="device-card">
                    <ng-template pTemplate="header">
                      <i class="pi pi-circle"></i>
                      <h3>
                      </ng-template>
        <ng-template pTemplate="content">
                      <div class="device-info">
                        <div class="info-item">
                          <span class="label">Status:</span>
                          <p-chip [class]="'status-' + device.status">{{ device.status }}</p-chip>
                        </div>
                        <div class="info-item">
                          <span class="label">Last Seen:</span>
                          <span>{{ device.lastSeen | date:'short' }}</span>
                        </div>
                        <div class="info-item">
                          <span class="label">Version:</span>
                          <span>{{ device.version }}</span>
                        </div>

                      <div class="device-qr">
                        <h4>Device URL</h4>
                        <div class="qr-placeholder">
                          <i class="pi pi-circle"></i>
                          <p>QR Code</p>
                        </div>
                        <p class="url-text">{{ device.url }}</p>
                      </div><ng-template pTemplate="footer">
                      <p-button (click)="editDevice(device)">
                        <i class="pi pi-pencil"></i>
                        Edit
                      </p-button>
                      <p-button (click)="copyDeviceUrl(device.url)">
                        <i class="pi pi-circle"></i>
                        Copy URL
                      </p-button>
                      <p-button color="warn" (click)="removeDevice(device)">
                        <i class="pi pi-trash"></i>
                        Remove
                      </p-button>
        </ng-template>
      </p-card>
                </div>

                <div *ngIf="devices.length === 0" class="empty-state">
                  <i class="pi pi-circle"></i>
                  <h3>No devices configured</h3>
                  <p>Add your first time clock device to get started</p>
                  <p-button severity="primary" (click)="addDevice()">
                    <i class="pi pi-plus"></i>
                    Add First Device
                  </p-button>
                </div>
            </p-tabPanel>

            <!-- PIN Management Tab -->
            <p-tabPanel label="PIN Management">
              <div class="tab-content">
                <div class="section-header">
                  <h3>Staff PIN Codes</h3>
                  <div class="header-actions">
                    <p-button severity="primary" (click)="generateAllPins()">
                      <i class="pi pi-refresh"></i>
                      Generate All PINs
                    </p-button>
                    <p-button outlined="true" (click)="exportPins()">
                      <i class="pi pi-circle"></i>
                      Export PINs
                    </p-button>
                  </div>

                <mat-table [dataSource]="staffPins" class="pins-table">
                  <!-- Column: staff -->
                    <th *matHeaderCellDef>Staff Member</th>
                    <td *matCellDef="let pin">{{ pin.staffName }}</td>
                  </ng-container>

                  <!-- Column: pin -->
                    <th *matHeaderCellDef>PIN</th>
                    <td *matCellDef="let pin">
                      <span class="pin-display">{{ showPins ? pin.pin : '****' }}</span>
                    </td>
                  </ng-container>

                  <!-- Column: status -->
                    <th *matHeaderCellDef>Status</th>
                    <td *matCellDef="let pin">
                      <p-chip [class]="pin.isActive ? 'status-active' : 'status-inactive'">
                        {{ pin.isActive ? 'Active' : 'Inactive' }}
                      </p-chip>
                    </td>
                  </ng-container>

                  <!-- Column: lastUsed -->
                    <th *matHeaderCellDef>Last Used</th>
                    <td *matCellDef="let pin">
                      {{ pin.lastUsed ? (pin.lastUsed | date:'short') : 'Never' }}
                    </td>
                  </ng-container>

                  <!-- Column: actions -->
                    <th *matHeaderCellDef>Actions</th>
                    <td *matCellDef="let pin">
                      <p-button text="true" (click)="regeneratePin(pin)">
                        <i class="pi pi-refresh"></i>
                      </p-button>
                      <p-button text="true" (click)="togglePinStatus(pin)">
                        <i class="pi pi-circle"></i>
                      </p-button>
                    </td>
                  </ng-container>

                  <tr *matHeaderRowDef="pinColumns"></tr>
                  <tr *matRowDef="let row; columns: pinColumns;"></tr>
                </mat-table>

                <div class="pin-controls">
                  <p-toggleButton [(ngModel)]="showPins">
                    Show PINs
                  </p-toggleButton>
                </div>
            </p-tabPanel>

            <!-- Clock Settings Tab -->
            <p-tabPanel label="Clock Settings">
              <div class="tab-content">
                <form [formGroup]="settingsForm" (ngSubmit)="saveSettings()">
                  <h3>Time Clock Policies</h3>

                  <div class="settings-grid">
                    <div class="p-field">
                      <label>Early Clock-in (minutes)</label>
                      <input pInputText type="number" formControlName="earlyClockInMinutes">
                      <mat-hint>How early staff can clock in before their shift</mat-hint>
                    </div>

                    <div class="p-field">
                      <label>Late Clock-out (minutes)</label>
                      <input pInputText type="number" formControlName="lateClockOutMinutes">
                      <mat-hint>How late staff can clock out after their shift</mat-hint>
                    </div>

                    <div class="p-field">
                      <label>PIN Length</label>
                      <p-dropdown formControlName="pinLength" [options]="dropdownOptions" optionLabel="label" optionValue="value"></p-dropdown>
                    </div>

                    <div class="p-field">
                      <label>PIN Expiration (days)</label>
                      <input pInputText type="number" formControlName="pinExpirationDays">
                      <mat-hint>0 = never expires</mat-hint>
                    </div>

                  <div class="toggle-settings">
                    <p-toggleButton formControlName="requireLocation">
                      Require location verification
                    </p-toggleButton>

                    <p-toggleButton formControlName="automaticBreaks">
                      Automatic break detection
                    </p-toggleButton>

                    <p-toggleButton formControlName="requireApprovalForOvertime">
                      Require approval for overtime
                    </p-toggleButton>

                    <p-toggleButton formControlName="requireApprovalForManualEntries">
                      Require approval for manual time entries
                    </p-toggleButton>
                  </div>

                  <div class="form-actions">
                    <p-button severity="primary" type="submit" [disabled]="settingsForm.invalid">
                      <i class="pi pi-save"></i>
                      Save Settings
                    </p-button>
                    <p-button type="button" (click)="resetSettings()">
                      <i class="pi pi-refresh"></i>
                      Reset to Defaults
                    </p-button>
                  </div>
                </form>
              </div>
            </p-tabPanel>

          </p-tabView</ng-template>
      </p-card>
    </div>
  `,
  styles: [`
    .timehub-setup-container {
      padding: 24px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .tab-content {
      padding: 24px 0;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }

    .section-header h3 {
      margin: 0;
      font-weight: 500;
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }

    .devices-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 24px;
    }

    .device-card {
      .status-online mat-icon { color: #4caf50; }
      .status-offline mat-icon { color: #f44336; }
      .status-maintenance mat-icon { color: #ff9800; }
    }

    .device-info {
      margin-bottom: 16px;
    }

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .label {
      font-weight: 500;
      color: #666;
    }

    .device-qr {
      text-align: center;
      margin: 16px 0;
    }

    .device-qr h4 {
      margin: 0 0 12px 0;
      font-size: 0.875rem;
      color: #666;
    }

    .qr-placeholder {
      width: 120px;
      height: 120px;
      border: 2px dashed #ccc;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      color: #666;
    }

    .qr-placeholder mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
    }

    .qr-placeholder p {
      margin: 8px 0 0 0;
      font-size: 0.75rem;
    }

    .url-text {
      font-family: monospace;
      font-size: 0.75rem;
      color: #666;
      margin: 8px 0 0 0;
      word-break: break-all;
    }

    .pins-table {
      width: 100%;
      margin-bottom: 16px;
    }

    .pin-display {
      font-family: monospace;
      font-weight: 500;
    }

    .pin-controls {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;
    }

    .settings-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
      margin-bottom: 24px;
    }

    .toggle-settings {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-bottom: 24px;
    }

    .form-actions {
      display: flex;
      gap: 12px;
    }

    .empty-state {
      text-align: center;
      padding: 48px;
      color: #666;
    }

    .empty-state mat-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .status-online { background-color: #4caf50; color: white; }
    .status-offline { background-color: #f44336; color: white; }
    .status-maintenance { background-color: #ff9800; color: white; }
    .status-active { background-color: #4caf50; color: white; }
    .status-inactive { background-color: #9e9e9e; color: white; }

    @media (max-width: 768px) {
      .timehub-setup-container {
        padding: 16px;
      }

      .devices-grid {
        grid-template-columns: 1fr;
      }

      .section-header {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
      }

      .header-actions {
        justify-content: center;
      }

      .settings-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class TimeHubSetupComponent implements OnInit {
  private fb = inject(FormBuilder);
  private dialog = inject(DialogService);
  private messageService = inject(MessageService);

  settingsForm!: FormGroup;
  showPins = false;
  pinColumns = ['staff', 'pin', 'status', 'lastUsed', 'actions'];

  devices: TimeClockDevice[] = [
    {
      id: '1',
      name: 'Front Desk Kiosk',
      location: 'Main Entrance',
      url: `${window.location.origin}/time-hub?kiosk=true&device=front-desk`,
      status: 'online',
      lastSeen: new Date(),
      version: '1.0.0',
      settings: {
        allowEarlyClockIn: true,
        requireLocation: false,
        autoBreaks: true
      }
    }
  ];

  staffPins: StaffPin[] = [
    {
      staffId: '1',
      staffName: 'John Smith',
      pin: '1234',
      isActive: true,
      lastUsed: new Date(Date.now() - 2 * 60 * 60 * 1000),
      failedAttempts: 0
    },
    {
      staffId: '2',
      staffName: 'Sarah Johnson',
      pin: '5678',
      isActive: true,
      failedAttempts: 0
    }
  ];

  ngOnInit(): void {
    this.initializeForm();
  }

  private initializeForm(): void {
    this.settingsForm = this.fb.group({
      earlyClockInMinutes: [15, [Validators.min(0), Validators.max(60)]],
      lateClockOutMinutes: [15, [Validators.min(0), Validators.max(60)]],
      pinLength: [4, Validators.required],
      pinExpirationDays: [0, Validators.min(0)],
      requireLocation: [false],
      automaticBreaks: [true],
      requireApprovalForOvertime: [true],
      requireApprovalForManualEntries: [false]
    });
  }

  addDevice(): void {
    // Implementation for adding new device
    this.messageService.add({ severity: 'info', summary: 'Add device functionality coming soon' });
  }

  editDevice(device: TimeClockDevice): void {
    // Implementation for editing device
    this.messageService.add({ severity: 'info', summary: `Edit ${device.name} functionality coming soon` });
  }

  removeDevice(device: TimeClockDevice): void {
    // Implementation for removing device
    this.messageService.add({ severity: 'info', summary: `Remove ${device.name} functionality coming soon` });
  }

  copyDeviceUrl(url: string): void {
    navigator.clipboard.writeText(url).then(() => {
      this.messageService.add({ severity: 'info', summary: 'Device URL copied to clipboard!' });
    });
  }

  generateAllPins(): void {
    this.staffPins.forEach(pin => {
      pin.pin = this.generateRandomPin();
    });
    this.messageService.add({ severity: 'info', summary: 'All PINs regenerated successfully' });
  }

  regeneratePin(pin: StaffPin): void {
    pin.pin = this.generateRandomPin();
    this.messageService.add({ severity: 'info', summary: `PIN regenerated for ${pin.staffName}` });
  }

  togglePinStatus(pin: StaffPin): void {
    pin.isActive = !pin.isActive;
    const status = pin.isActive ? 'activated' : 'deactivated';
    this.messageService.add({ severity: 'info', summary: `PIN ${status} for ${pin.staffName}` });
  }

  exportPins(): void {
    // Implementation for exporting PINs
    this.messageService.add({ severity: 'info', summary: 'Export PINs functionality coming soon' });
  }

  saveSettings(): void {
    if (this.settingsForm.valid) {
      // Implementation for saving settings
      this.messageService.add({ severity: 'info', summary: 'Settings saved successfully' });
    }
  }

  resetSettings(): void {
    this.initializeForm();
    this.messageService.add({ severity: 'info', summary: 'Settings reset to defaults' });
  }

  private generateRandomPin(): string {
    const length = this.settingsForm?.get('pinLength')?.value || 4;
    return Math.floor(Math.random() * Math.pow(10, length))
      .toString()
      .padStart(length, '0');
  }
}
