import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { CheckboxModule } from 'primeng/checkbox';
import { SliderModule } from 'primeng/slider';
import { TabViewModule } from 'primeng/tabview';
import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { ChipModule } from 'primeng/chip';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';

export interface CustomWidgetConfig {
  id?: string;
  title: string;
  type: 'number' | 'chart' | 'list' | 'table' | 'gauge' | 'progress' | 'custom';
  size: 'small' | 'medium' | 'large' | 'xlarge';
  dataSource: {
    type: 'static' | 'api' | 'database' | 'realtime';
    endpoint?: string;
    query?: string;
    refreshInterval?: number;
  };
  visualization: {
    chartType?: 'line' | 'bar' | 'pie' | 'doughnut' | 'area' | 'scatter';
    colors?: string[];
    showLegend?: boolean;
    showGrid?: boolean;
    animation?: boolean;
  };
  styling: {
    backgroundColor?: string;
    textColor?: string;
    borderRadius?: number;
    padding?: number;
    fontSize?: number;
  };
  filters?: {
    dateRange?: boolean;
    businessFilter?: boolean;
    customFilters?: string[];
  };
  permissions?: {
    viewRoles?: string[];
    editRoles?: string[];
  };
}

@Component({
  selector: 'app-custom-widget-builder',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonModule, InputTextModule, DropdownModule,
    CheckboxModule,
    SliderModule,
    TabViewModule,
    CardModule,
    DividerModule,
    ChipModule
  ],
  template: `
    <div class="widget-builder-dialog">
      <h2 class="dialog-title">
        <i class="pi pi-circle"></i>
        Custom Widget Builder
      </h2>

      <div class="dialog-content">
        <form [formGroup]="widgetForm" class="widget-form">
          <p-tabView>
            <!-- Basic Configuration Tab -->
            <p-tabPanel label="Basic">
              <div class="tab-content">
                <div class="p-field" appearance="outline" class="full-width">
                  <label>Widget Title</label>
                  <input pInputText formControlName="title" placeholder="Enter widget title">
                  <small class="p-error" *ngIf="widgetForm.get('title')?.hasError('required')">
                    Title is required
                  </small>
                </div>

                <div class="p-field full-width">
                  <label>Widget Type</label>
                  <p-dropdown formControlName="type" [options]="widgetTypeOptions" optionLabel="label" optionValue="value"></p-dropdown>
                </div>

                <div class="p-field full-width">
                  <label>Widget Size</label>
                  <p-dropdown formControlName="size" [options]="sizeOptions" optionLabel="label" optionValue="value"></p-dropdown>
                </div>
              </div>
            </p-tabPanel>

            <!-- Data Source Tab -->
            <p-tabPanel header="Data Source">
              <div class="tab-content" formGroupName="dataSource">
                <div class="p-field full-width">
                  <label>Data Source Type</label>
                  <p-dropdown formControlName="type" [options]="dataSourceOptions" optionLabel="label" optionValue="value"></p-dropdown>
                </div>

                <div class="p-field full-width"
                     *ngIf="widgetForm.get('dataSource.type')?.value !== 'static'">
                  <label>Endpoint/Query</label>
                  <textarea pInputText formControlName="endpoint"
                           placeholder="Enter API endpoint or database query"
                           rows="3"></textarea>
                </div>

                <div class="p-field full-width">
                  <label>Refresh Interval (seconds)</label>
                  <input pInputText type="number" formControlName="refreshInterval"
                         placeholder="60" min="5" max="3600">
                </div>
              </div>
            </p-tabPanel>

            <!-- Visualization Tab -->
            <p-tabPanel header="Visualization" *ngIf="widgetForm.get('type')?.value === 'chart'">
              <div class="tab-content" formGroupName="visualization">
                <div class="p-field full-width">
                  <label>Chart Type</label>
                  <p-dropdown formControlName="chartType" [options]="chartTypeOptions" optionLabel="label" optionValue="value"></p-dropdown>
                </div>

                <div class="checkbox-group">
                  <p-checkbox formControlName="showLegend" binary="true">Show Legend</p-checkbox>
                  <p-checkbox formControlName="showGrid" binary="true">Show Grid</p-checkbox>
                  <p-checkbox formControlName="animation" binary="true">Enable Animation</p-checkbox>
                </div>
              </div>
            </p-tabPanel>

            <!-- Styling Tab -->
            <p-tabPanel header="Styling">
              <div class="tab-content" formGroupName="styling">
                <div class="p-field half-width">
                  <label>Background Color</label>
                  <input pInputText type="color" formControlName="backgroundColor">
                </div>

                <div class="p-field half-width">
                  <label>Text Color</label>
                  <input pInputText type="color" formControlName="textColor">
                </div>

                <div class="slider-group">
                  <label>Border Radius: {{widgetForm.get('styling.borderRadius')?.value}}px</label>
                  <p-slider [min]="0" [max]="20" [step]="1" formControlName="borderRadius"></p-slider>
                </div>

                <div class="slider-group">
                  <label>Padding: {{widgetForm.get('styling.padding')?.value}}px</label>
                  <p-slider [min]="8" [max]="32" [step]="2" formControlName="padding"></p-slider>
                </div>

                <div class="slider-group">
                  <label>Font Size: {{widgetForm.get('styling.fontSize')?.value}}px</label>
                  <p-slider [min]="10" [max]="24" [step]="1" formControlName="fontSize"></p-slider>
                </div>
              </div>
            </p-tabPanel>

            <!-- Filters Tab -->
            <p-tabPanel header="Filters">
              <div class="tab-content" formGroupName="filters">
                <div class="checkbox-group">
                  <p-checkbox formControlName="dateRange" binary="true">Date Range Filter</p-checkbox>
                  <p-checkbox formControlName="businessFilter" binary="true">Business Filter</p-checkbox>
                </div>
              </div>
            </p-tabPanel>
          </p-tabView>
        </form>

        <!-- Preview Section -->
        <p-card class="preview-card">
          <ng-template pTemplate="header">
            <h3>Widget Preview</h3>
          </ng-template>

          <ng-template pTemplate="content">
            <div class="widget-preview" [ngStyle]="getPreviewStyles()">
              <h3>{{widgetForm.get('title')?.value || 'Widget Title'}}</h3>
              <div class="preview-content">
                <ng-container [ngSwitch]="widgetForm.get('type')?.value">
                  <div *ngSwitchCase="'number'" class="number-preview">
                    <span class="number">42</span>
                    <span class="label">Sample Value</span>
                  </div>
                  <div *ngSwitchCase="'chart'" class="chart-preview">
                    📊 {{widgetForm.get('visualization.chartType')?.value || 'Chart'}} Preview
                  </div>
                  <div *ngSwitchDefault class="default-preview">
                    {{widgetForm.get('type')?.value || 'Widget'}} Content
                  </div>
                </ng-container>
              </div>
            </div>
          </ng-template>
        </p-card>
      </div>

      <div class="dialog-actions">
        <p-button label="Cancel" severity="secondary" (click)="onCancel()"></p-button>
        <p-button label="Create Widget"
                [disabled]="widgetForm.invalid"
                (click)="onSave()">
        </p-button>
      </div>
  `,
  styleUrls: ['./custom-widget-builder.component.scss']
})
export class CustomWidgetBuilderComponent {
  widgetForm: FormGroup;

  // Dropdown options for various fields
  getDropdownOptions(field: string) {
    switch (field) {
      case 'type':
        return this.widgetTypeOptions;
      case 'size':
        return this.sizeOptions;
      case 'dataSource':
        return this.dataSourceOptions;
      case 'chartType':
        return this.chartTypeOptions;
      default:
        return [];
    }
  }

  // Specific dropdown options for different contexts
  get widgetTypeOptions() {
    return [
      { label: 'Number Widget', value: 'number' },
      { label: 'Chart Widget', value: 'chart' },
      { label: 'List Widget', value: 'list' },
      { label: 'Text Widget', value: 'text' }
    ];
  }

  get sizeOptions() {
    return [
      { label: 'Small', value: 'small' },
      { label: 'Medium', value: 'medium' },
      { label: 'Large', value: 'large' }
    ];
  }

  get dataSourceOptions() {
    return [
      { label: 'Static Data', value: 'static' },
      { label: 'API Endpoint', value: 'api' },
      { label: 'Database Query', value: 'database' }
    ];
  }

  get chartTypeOptions() {
    return [
      { label: 'Line Chart', value: 'line' },
      { label: 'Bar Chart', value: 'bar' },
      { label: 'Pie Chart', value: 'pie' },
      { label: 'Doughnut Chart', value: 'doughnut' }
    ];
  }

  constructor(
    private fb: FormBuilder,
    @Inject(DynamicDialogRef) public dialogRef: DynamicDialogRef,
    @Inject(DynamicDialogConfig) public data: { config?: CustomWidgetConfig }
  ) {
    this.widgetForm = this.createForm();

    if (data?.config) {
      this.widgetForm.patchValue(data.config);
    }
  }

  private createForm(): FormGroup {
    return this.fb.group({
      title: ['', Validators.required],
      type: ['number', Validators.required],
      size: ['medium', Validators.required],
      dataSource: this.fb.group({
        type: ['static'],
        endpoint: [''],
        refreshInterval: [60]
      }),
      visualization: this.fb.group({
        chartType: ['line'],
        showLegend: [true],
        showGrid: [true],
        animation: [true]
      }),
      styling: this.fb.group({
        backgroundColor: ['#ffffff'],
        textColor: ['#333333'],
        borderRadius: [8],
        padding: [16],
        fontSize: [14]
      }),
      filters: this.fb.group({
        dateRange: [false],
        businessFilter: [false]
      })
    });
  }

  getPreviewStyles() {
    const styling = this.widgetForm.get('styling')?.value;
    return {
      'background-color': styling?.backgroundColor || '#ffffff',
      'color': styling?.textColor || '#333333',
      'border-radius': `${styling?.borderRadius || 8}px`,
      'padding': `${styling?.padding || 16}px`,
      'font-size': `${styling?.fontSize || 14}px`
    };
  }

  onSave() {
    if (this.widgetForm.valid) {
      const config: CustomWidgetConfig = {
        ...this.widgetForm.value,
        id: this.data?.config?.id || `custom-${Date.now()}`
      };
      this.dialogRef.close(config);
    }
  }

  onCancel() {
    this.dialogRef.close();
  }
}
