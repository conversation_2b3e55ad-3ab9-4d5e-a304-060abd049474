import { Component, Input, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import { DividerModule } from 'primeng/divider';
import { TooltipModule } from 'primeng/tooltip';
import { StaffManagerThemeService } from '../../core/theme/staffmanager-theme';
import { AuthService } from '../../core/auth/auth.service';

@Component({
  selector: 'app-user-icon', // RENAMED: UserMenu -> UserIcon
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    MenuModule,
    DividerModule,
    TooltipModule
  ],
  template: `
    <div class="user-icon-container"
         [class.collapsed]="collapsed"
         [class.dark-theme]="themeService.isDark()"
         style="position: absolute !important; bottom: 0 !important; left: 0 !important; right: 0 !important;
                display: flex !important; flex-direction: column !important;
                background: rgba(255, 255, 255, 0.95) !important; border-top: 1px solid rgba(0, 0, 0, 0.08) !important;
                padding: 8px !important; height: auto !important; min-height: 120px !important;">
      <!-- CRITICAL: User Actions ABOVE UserIcon -->
      <div class="user-actions" [class.collapsed]="collapsed"
           style="display: flex !important; flex-direction: column !important; gap: 4px !important;
                  order: 1 !important; flex-shrink: 0 !important; width: 100% !important;
                  align-items: stretch !important; padding: 4px !important; margin-bottom: 8px !important;">
        <!-- Change User Button -->
        <p-button (onClick)="changeUser()"
                  icon="pi pi-refresh"
                  text="true"
                  class="user-action-btn change-user-btn"
                  [pTooltip]="collapsed ? 'Change User' : ''"
                  tooltipPosition="right"
                  aria-label="Change User"
                  [ngStyle]="{
                    'background': 'rgba(255, 255, 255, 0.8)',
                    'border': '1px solid rgba(25, 118, 210, 0.1)',
                    'min-height': '44px',
                    'border-radius': '6px',
                    'padding': '8px 12px',
                    'display': 'flex',
                    'align-items': 'center',
                    'justify-content': 'flex-start',
                    'width': '100%'
                  }">
          <span *ngIf="!collapsed" class="action-text" style="margin-left: 8px;">Change User</span>
        </p-button>

        <!-- Sign Out Button -->
        <p-button (onClick)="signOut()"
                  icon="pi pi-sign-out"
                  text="true"
                  class="user-action-btn sign-out-btn"
                  [pTooltip]="collapsed ? 'Sign Out' : ''"
                  tooltipPosition="right"
                  aria-label="Sign Out"
                  [ngStyle]="{
                    'background': 'rgba(255, 255, 255, 0.8)',
                    'border': '1px solid rgba(25, 118, 210, 0.1)',
                    'min-height': '44px',
                    'border-radius': '6px',
                    'padding': '8px 12px',
                    'display': 'flex',
                    'align-items': 'center',
                    'justify-content': 'flex-start',
                    'width': '100%'
                  }">
          <span *ngIf="!collapsed" class="action-text" style="margin-left: 8px;">Sign Out</span>
        </p-button>
      </div>

      <!-- UserIcon Section - BELOW actions, clickable for View Profile -->
      <div class="user-info-section" [class.collapsed]="collapsed"
           style="display: flex !important; order: 2 !important; flex-shrink: 0 !important;
                  width: 100% !important; justify-content: center !important; align-items: center !important;
                  padding: 4px !important; margin-top: 8px !important;">
        <!-- Collapsed state: Only avatar icon - clickable -->
        <p-button *ngIf="collapsed"
                  (onClick)="viewProfile()"
                  icon="pi pi-user"
                  text="true"
                  [rounded]="true"
                  class="user-avatar-btn"
                  [pTooltip]="'View Profile - ' + userName + ' (' + userRole + ')'"
                  tooltipPosition="right"
                  aria-label="View Profile"
                  [ngStyle]="{
                    'width': '48px',
                    'height': '48px',
                    'font-size': '1.5rem'
                  }">
        </p-button>

        <!-- Expanded state: Avatar + name - clickable -->
        <p-button *ngIf="!collapsed"
                  (onClick)="viewProfile()"
                  text="true"
                  class="user-info-btn"
                  pTooltip="View Profile"
                  tooltipPosition="right"
                  aria-label="View Profile"
                  [ngStyle]="{
                    'width': '100%',
                    'padding': '8px',
                    'justify-content': 'flex-start'
                  }">
          <div class="user-info" style="display: flex; align-items: center; width: 100%;">
            <i class="pi pi-user" style="font-size: 1.5rem; margin-right: 12px; color: #666;"></i>
            <div class="user-details" style="display: flex; flex-direction: column; align-items: flex-start;">
              <span class="user-name" style="font-weight: 600; color: #333;">{{ userName }}</span>
              <span class="user-role" style="font-size: 0.85rem; color: #666;">{{ userRole }}</span>
            </div>
          </div>
        </p-button>
      </div>
    </div>
  `,
  styleUrls: ['./user-menu.component.scss']
})
export class UserIconComponent {
  @Input() collapsed = false;
  @Input() userName = 'John Doe';
  @Input() userRole = 'Manager';
  @Input() userEmail = '<EMAIL>';

  private authService = inject(AuthService);
  private router = inject(Router);

  constructor(public themeService: StaffManagerThemeService) {
    // Update user info from auth service
    this.authService.userProfile$.subscribe(profile => {
      if (profile) {
        this.userName = profile.displayName;
        this.userRole = profile.role;
        this.userEmail = profile.email;
      }
    });
  }

  viewProfile() {
    this.authService.userProfile$.subscribe(profile => {
      if (profile?.staffId) {
        // User has a staff profile, navigate directly to it
        this.router.navigate(['/staff/profile', profile.staffId]);
      } else {
        // User needs profile setup, navigate to profile redirect component
        this.router.navigate(['/staff/my-profile']);
      }
    });
  }

  changeUser() {
    // Sign out and redirect to login
    this.authService.signOut().subscribe(() => {
      this.router.navigate(['/auth/login']);
    });
  }

  signOut() {
    this.authService.signOut().subscribe(() => {
      this.router.navigate(['/auth/login']);
    });
  }
}
