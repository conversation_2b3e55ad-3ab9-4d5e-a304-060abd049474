import { Component, Input, Output, EventEmitter } from '@angular/core';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { ChipModule } from 'primeng/chip';
import { TooltipModule } from 'primeng/tooltip';
export interface Business {
  id: string;
  name: string;
  logoUrl?: string;
}

@Component({
  selector: 'app-business-selector',
  standalone: true,
  imports: [
    CommonModule, ButtonModule, ChipModule, TooltipModule
  ],
  templateUrl: './business-selector.component.html',
  styleUrl: './business-selector.component.scss'
})
export class BusinessSelectorComponent {
  /** List of all businesses/locations */
  @Input() businesses: Business[] = [];
  /** Array of selected business IDs */
  @Input() selectedBusinessIds: string[] = [];
  /** If sidebar is collapsed */
  @Input() collapsed = false;
  /** Emit when selection changes */
  @Output() selectionChange = new EventEmitter<string[]>();

  private dialogRef: DynamicDialogRef | undefined;

  constructor(private dialogService: DialogService) {}

  get selectedBusinesses(): Business[] {
    return this.businesses.filter(b => this.selectedBusinessIds.includes(b.id));
  }

  get isOneView(): boolean {
    return this.selectedBusinessIds.length > 1;
  }

  openSelectorDialog() {
    // Dynamic import to avoid circular dependency and SSR issues
    import('./business-selector-dialog/business-selector-dialog.component').then(({ BusinessSelectorDialogComponent }) => {
      this.dialogRef = this.dialogService.open(BusinessSelectorDialogComponent, {
        data: {
          businesses: this.businesses,
          selectedBusinessIds: this.selectedBusinessIds
        },
        header: 'Select Businesses',
        width: '360px',
        modal: true,
        closable: true
      });

      this.dialogRef.onClose.subscribe((result: string[] | null) => {
        if (result) {
          this.selectionChange.emit(result);
        }
      });
    });
  }

  /** For accessibility: ARIA label for the selector */
  get ariaLabel(): string {
    return this.isOneView
      ? `OneView active: ${this.selectedBusinesses.map(b => b.name).join(', ')}`
      : `Current business: ${this.selectedBusinesses[0]?.name || 'None'}`;
  }
}
