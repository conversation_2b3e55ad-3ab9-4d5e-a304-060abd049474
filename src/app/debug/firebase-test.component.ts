import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardModule } from 'primeng/card';
import { ToastModule } from 'primeng/toast';
import { ButtonModule } from 'primeng/button';
import { MessageService } from 'primeng/api';
import { Router } from '@angular/router';
import { FirebaseContextService } from '../core/services/firebase-context.service';
import { AuthService } from '../core/auth/auth.service';

/**
 * Firebase Test Component
 *
 * Simple component to test Firebase operations and debug issues
 */
@Component({
  selector: 'app-firebase-test',
  standalone: true,
  imports: [
    ToastModule,
    CommonModule,
    CardModule,
    ButtonModule
  ],
  template: `
    <div style="padding: 24px; max-width: 600px; margin: 0 auto;">
      <p-card>
        <ng-template pTemplate="header">
          <h3>Firebase Test</h3>
        </ng-template>

        <ng-template pTemplate="content">
          <div style="display: flex; flex-direction: column; gap: 16px; margin-top: 16px;">
            <p-button severity="primary" (click)="testFirebaseWrite()">
              Test Firebase Write
            </p-button>

            <p-button severity="secondary" (click)="testFirebaseRead()">
              Test Firebase Read
            </p-button>

            <p-button (click)="testUserProfile()">
              Test User Profile
            </p-button>

            <p-button severity="secondary" (click)="testEditProfileNavigation()">
              Test Edit Profile Navigation
            </p-button>

            <p-button color="warn" (click)="clearTestData()">
              Clear Test Data
            </p-button>
          </div>

          <div *ngIf="lastResult" style="margin-top: 24px; padding: 16px; background: #f5f5f5; border-radius: 4px;">
            <h4>Last Result:</h4>
            <pre>{{ lastResult }}</pre>
          </div>
        </ng-template>
      </p-card>
    </div>
  `
})
export class FirebaseTestComponent {
  private firebaseContext = inject(FirebaseContextService);
  private authService = inject(AuthService);
  private router = inject(Router);
  private messageService = inject(MessageService);

  lastResult = '';

  testFirebaseWrite(): void {
    console.log('🧪 Testing Firebase write...');

    const testData = {
      message: 'Hello Firebase!',
      timestamp: new Date(),
      testId: Date.now().toString()
    };

    this.firebaseContext.setDocument('test/write-test', testData).subscribe({
      next: () => {
        console.log('✅ Firebase write successful');
        this.lastResult = 'Write successful: ' + JSON.stringify(testData, null, 2);
        this.messageService.add({ severity: 'info', summary: 'Firebase write successful!' });
      },
      error: (error) => {
        console.error('❌ Firebase write failed:', error);
        this.lastResult = 'Write failed: ' + error.message;
        this.messageService.add({ severity: 'info', summary: 'Firebase write failed: ' + error.message });
      }
    });
  }

  testFirebaseRead(): void {
    console.log('🧪 Testing Firebase read...');

    this.firebaseContext.getDocument('test/write-test').subscribe({
      next: (docSnap) => {
        if (docSnap?.exists()) {
          const data = docSnap.data();
          console.log('✅ Firebase read successful:', data);
          this.lastResult = 'Read successful: ' + JSON.stringify(data, null, 2);
          this.messageService.add({ severity: 'info', summary: 'Firebase read successful!' });
        } else {
          console.log('📄 Document does not exist');
          this.lastResult = 'Document does not exist';
          this.messageService.add({ severity: 'info', summary: 'Document does not exist' });
        }
      },
      error: (error) => {
        console.error('❌ Firebase read failed:', error);
        this.lastResult = 'Read failed: ' + error.message;
        this.messageService.add({ severity: 'info', summary: 'Firebase read failed: ' + error.message });
      }
    });
  }

  testUserProfile(): void {
    console.log('🧪 Testing user profile...');

    this.authService.userProfile$.subscribe({
      next: (profile) => {
        if (profile) {
          console.log('✅ User profile loaded:', profile);
          this.lastResult = 'User profile: ' + JSON.stringify(profile, null, 2);
          this.messageService.add({ severity: 'info', summary: 'User profile loaded successfully!' });
        } else {
          console.log('👤 No user profile found');
          this.lastResult = 'No user profile found';
          this.messageService.add({ severity: 'info', summary: 'No user profile found' });
        }
      },
      error: (error) => {
        console.error('❌ User profile failed:', error);
        this.lastResult = 'User profile failed: ' + error.message;
        this.messageService.add({ severity: 'info', summary: 'User profile failed: ' + error.message });
      }
    });
  }

  clearTestData(): void {
    console.log('🧹 Clearing test data...');

    this.firebaseContext.deleteDocument('test/write-test').subscribe({
      next: () => {
        console.log('✅ Test data cleared');
        this.lastResult = 'Test data cleared successfully';
        this.messageService.add({ severity: 'info', summary: 'Test data cleared!' });
      },
      error: (error) => {
        console.error('❌ Clear test data failed:', error);
        this.lastResult = 'Clear failed: ' + error.message;
        this.messageService.add({ severity: 'info', summary: 'Clear failed: ' + error.message });
      }
    });
  }

  testEditProfileNavigation(): void {
    console.log('🧪 Testing Edit Profile Navigation...');

    this.authService.userProfile$.subscribe({
      next: (profile) => {
        if (profile) {
          console.log('✅ Current user profile:', profile);
          this.lastResult = 'Current user profile: ' + JSON.stringify(profile, null, 2);

          if (profile.staffId) {
            console.log('🔗 Attempting to navigate to edit profile:', `/staff/edit/${profile.staffId}`);
            this.router.navigate(['/staff/edit', profile.staffId]);
          } else {
            console.log('❌ No staffId found in user profile');
            this.lastResult += '\n\n❌ No staffId found - cannot navigate to edit profile';
            this.messageService.add({ severity: 'info', summary: 'No staffId found in user profile' });
          }
        } else {
          console.log('❌ No user profile found');
          this.lastResult = 'No user profile found';
          this.messageService.add({ severity: 'info', summary: 'No user profile found' });
        }
      },
      error: (error) => {
        console.error('❌ Error getting user profile:', error);
        this.lastResult = 'Error getting user profile: ' + error.message;
        this.messageService.add({ severity: 'info', summary: 'Error getting user profile: ' + error.message });
      }
    });
  }
}
