#!/usr/bin/env node

/**
 * Template Structure Fix Script - Fixes all remaining template structure issues
 */

const fs = require('fs');
const path = require('path');

class TemplateStructureFix {
  constructor() {
    this.srcPath = './src/app';
    this.fixes = 0;
  }

  // Fix all template structure issues comprehensively
  fixTemplateStructures(content) {
    let fixed = content;

    // Define all the template structure fixes needed
    const fixes = [
      // Fix p-tabPanel extra closing tags
      { from: /<\/div><\/p-tabPanel><\/p-tabPanel>/g, to: '</div></p-tabPanel>' },
      { from: /<\/p-tabPanel><\/p-tabPanel>/g, to: '</p-tabPanel>' },
      
      // Fix p-card extra closing tags
      { from: /<\/ng-template><\/p-card><\/p-card>/g, to: '</ng-template></p-card>' },
      { from: /<\/div><\/ng-template><\/p-card>/g, to: '</div></ng-template></p-card>' },
      { from: /<\/p><\/ng-template><\/p-card>/g, to: '</p></ng-template></p-card>' },
      { from: /<\/span><\/ng-template><\/p-card>/g, to: '</span></ng-template></p-card>' },
      { from: /<\/p-button><\/ng-template><\/p-card>/g, to: '</p-button></ng-template></p-card>' },
      { from: /<\/p-button><\/p-card>/g, to: '</p-button></ng-template></p-card>' },
      
      // Fix ng-template extra closing tags
      { from: /<\/ng-template><\/ng-template>/g, to: '</ng-template>' },
      { from: /<\/div><\/ng-template><\/ng-template>/g, to: '</div></ng-template>' },
      
      // Fix p-button extra closing tags
      { from: /<\/p-button><\/p-button>/g, to: '</p-button>' },
      
      // Fix p-menu extra closing tags
      { from: /<\/p-menu><\/p-menu>/g, to: '</p-menu>' },
      
      // Fix p-toolbar extra closing tags
      { from: /<\/p-toolbar><\/p-toolbar>/g, to: '</p-toolbar>' },
      
      // Fix ng-container extra closing tags
      { from: /<\/ng-container><\/ng-container>/g, to: '</ng-container>' },
      
      // Fix table extra closing tags
      { from: /<\/table><\/table>/g, to: '</table>' },
      
      // Fix div extra closing tags
      { from: /<\/div><\/div><\/div>/g, to: '</div></div>' },
      
      // Fix form extra closing tags
      { from: /<\/form><\/form>/g, to: '</form>' },
      
      // Fix specific broken patterns from build errors
      { from: /<\/div><\/p-card>/g, to: '</div></ng-template></p-card>' },
      { from: /<\/p><\/p-card>/g, to: '</p></ng-template></p-card>' },
      
      // Fix broken p-button structures with content
      { from: /<p-button([^>]*)>\s*([^<]*)\s*<\/p-button>\s*<\/p-button>/g, to: '<p-button$1>$2</p-button>' },
      
      // Fix broken p-menu structures with content
      { from: /<p-menu([^>]*)>\s*([^<]*)\s*<\/p-menu>\s*<\/p-menu>/g, to: '<p-menu$1>$2</p-menu>' },
      
      // Fix broken ng-container structures with content
      { from: /<ng-container([^>]*)>\s*([^<]*)\s*<\/ng-container>\s*<\/ng-container>/g, to: '<ng-container$1>$2</ng-container>' },
      
      // Fix broken table structures with content
      { from: /<table([^>]*)>\s*([\s\S]*?)\s*<\/table>\s*<\/table>/g, to: '<table$1>$2</table>' },
      
      // Fix broken form structures with content
      { from: /<form([^>]*)>\s*([\s\S]*?)\s*<\/form>\s*<\/form>/g, to: '<form$1>$2</form>' },
      
      // Fix broken div structures with content
      { from: /<div([^>]*)>\s*([\s\S]*?)\s*<\/div>\s*<\/div>\s*<\/div>/g, to: '<div$1>$2</div></div>' }
    ];

    // Apply all fixes
    fixes.forEach(fix => {
      const beforeCount = (fixed.match(fix.from) || []).length;
      if (beforeCount > 0) {
        fixed = fixed.replace(fix.from, fix.to);
        this.fixes += beforeCount;
      }
    });

    return fixed;
  }

  // Process a single file
  processFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let fixed = content;

      // Apply template structure fixes
      fixed = this.fixTemplateStructures(fixed);

      // Only write if changes were made
      if (fixed !== content) {
        fs.writeFileSync(filePath, fixed, 'utf8');
        console.log(`✅ Fixed: ${filePath}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
      return false;
    }
  }

  // Find all files
  findFiles(dir, extensions = ['.ts', '.html']) {
    const files = [];
    
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          files.push(...this.findFiles(fullPath, extensions));
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${dir}:`, error.message);
    }
    
    return files;
  }

  // Run the template structure fix
  run() {
    console.log('🔧 Starting Template Structure Fix...\n');
    
    const files = this.findFiles(this.srcPath);
    console.log(`📁 Found ${files.length} files to process\n`);
    
    let processedFiles = 0;
    let modifiedFiles = 0;
    
    files.forEach(file => {
      processedFiles++;
      if (this.processFile(file)) {
        modifiedFiles++;
      }
    });
    
    console.log('\n📊 Template Structure Fix Summary:');
    console.log(`   Files processed: ${processedFiles}`);
    console.log(`   Files modified: ${modifiedFiles}`);
    console.log(`   Template fixes applied: ${this.fixes}`);
    console.log('\n🎯 All template structure errors should now be resolved!');
  }
}

// Run the script
if (require.main === module) {
  const fixer = new TemplateStructureFix();
  fixer.run();
}

module.exports = TemplateStructureFix;
