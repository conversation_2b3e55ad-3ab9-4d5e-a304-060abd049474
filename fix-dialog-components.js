#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Dialog component fixes
const dialogFixes = [
  // Replace Material Dialog elements with PrimeNG equivalents
  {
    pattern: /<h2 mat-dialog-title>/g,
    replacement: '<div class="dialog-header"><h2>'
  },
  {
    pattern: /<\/h2>\s*<mat-dialog-content>/g,
    replacement: '</h2></div>\n      <div class="dialog-content">'
  },
  {
    pattern: /<mat-dialog-content>/g,
    replacement: '<div class="dialog-content">'
  },
  {
    pattern: /<\/mat-dialog-content>/g,
    replacement: '</div>'
  },
  {
    pattern: /<mat-dialog-actions[^>]*>/g,
    replacement: '<div class="dialog-actions">'
  },
  {
    pattern: /<\/mat-dialog-actions>/g,
    replacement: '</div>'
  },
  
  // Replace Material form elements with PrimeNG equivalents
  {
    pattern: /<mat-error[^>]*>/g,
    replacement: '<small class="p-error"'
  },
  {
    pattern: /<\/mat-error>/g,
    replacement: '</small>'
  },
  {
    pattern: /<mat-hint[^>]*>/g,
    replacement: '<small class="p-help"'
  },
  {
    pattern: /<\/mat-hint>/g,
    replacement: '</small>'
  },
  
  // Fix button structure
  {
    pattern: /<button[^>]*p-button[^>]*>/g,
    replacement: '<p-button'
  },
  {
    pattern: /<\/p-button>/g,
    replacement: '</p-button>'
  },
  
  // Fix accordion structure
  {
    pattern: /<p-accordionTab-header>/g,
    replacement: '<ng-template pTemplate="header">'
  },
  {
    pattern: /<\/p-accordionTab-header>/g,
    replacement: '</ng-template>'
  },
  {
    pattern: /<mat-panel-title>/g,
    replacement: '<span>'
  },
  {
    pattern: /<\/mat-panel-title>/g,
    replacement: '</span>'
  },
  {
    pattern: /<mat-expansion-panel-header>/g,
    replacement: ''
  },
  {
    pattern: /<\/mat-expansion-panel-header>/g,
    replacement: ''
  },
  
  // Fix form field structure
  {
    pattern: /appearance="outline"/g,
    replacement: ''
  },
  {
    pattern: /class="p-field"[^>]*class="[^"]*"/g,
    replacement: 'class="p-field"'
  }
];

// Import fixes for dialog components
const importFixes = [
  {
    pattern: /DragDropModuleInputTextModule/g,
    replacement: 'DragDropModule,\n    InputTextModule'
  },
  {
    pattern: /ReactiveFormsModuleInputTextModule/g,
    replacement: 'ReactiveFormsModule,\n    InputTextModule'
  },
  {
    pattern: /InputTextModule, DropdownModule/g,
    replacement: 'InputTextModule,\n    DropdownModule'
  }
];

function fixDialogComponent(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Apply dialog fixes
    dialogFixes.forEach(fix => {
      const oldContent = content;
      content = content.replace(fix.pattern, fix.replacement);
      if (content !== oldContent) {
        modified = true;
      }
    });
    
    // Apply import fixes
    importFixes.forEach(fix => {
      const oldContent = content;
      content = content.replace(fix.pattern, fix.replacement);
      if (content !== oldContent) {
        modified = true;
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content);
      console.log(`Fixed dialog component: ${filePath}`);
    }
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error.message);
  }
}

function walkDirectory(dir) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
      walkDirectory(filePath);
    } else if (file.includes('dialog') && file.endsWith('.ts') && !file.endsWith('.spec.ts')) {
      fixDialogComponent(filePath);
    }
  });
}

console.log('Starting dialog component fixes...');
walkDirectory('./src');
console.log('Dialog component fixes completed!');
